"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PricingService = void 0;
const logger_1 = require("@/utils/logger");
const axios_1 = __importDefault(require("axios"));
const config_1 = require("@/config");
const abcp_service_1 = require("./abcp.service");
const exist_service_1 = require("./exist.service");
const client_1 = require("@prisma/client");
class PricingService {
    constructor() {
        this.priceDatabase = new Map();
        this.priceCache = new Map();
        this.CACHE_TTL = 30 * 60 * 1000;
        this.initializePriceDatabase();
        this.abcpService = new abcp_service_1.AbcpService();
        this.existService = new exist_service_1.ExistService();
        this.prisma = new client_1.PrismaClient();
    }
    async getModificationPricing(carInfo, modification) {
        try {
            const dbPricing = await this.getPricingFromDatabase(carInfo, modification);
            if (dbPricing) {
                logger_1.logger.debug(`Retrieved database pricing for ${modification}: ${dbPricing.price} ₽`);
                return dbPricing;
            }
            const externalPricing = await this.getExternalModificationPricing(carInfo, modification);
            if (externalPricing) {
                logger_1.logger.debug(`Retrieved external pricing for ${modification}: $${externalPricing.price}`);
                return externalPricing;
            }
            logger_1.logger.debug(`Using default pricing for ${modification}`);
            return this.getDefaultPricing(modification);
        }
        catch (error) {
            logger_1.logger.error('Error getting modification pricing:', error);
            return this.getDefaultPricing(modification);
        }
    }
    generateModificationKey(carInfo, modification) {
        const make = carInfo.make.toLowerCase();
        const model = carInfo.model.toLowerCase();
        const mod = modification.toLowerCase().replace(/\s+/g, '_');
        return `${make}_${model}_${mod}`;
    }
    async getPricingFromDatabase(carInfo, modification) {
        try {
            const parts = await this.prisma.modificationPart.findMany({
                where: {
                    OR: [
                        { name: { contains: modification, mode: 'insensitive' } },
                        { description: { contains: modification, mode: 'insensitive' } },
                    ],
                },
                include: {
                    category: true,
                },
                take: 5,
            });
            if (parts.length === 0) {
                return null;
            }
            const bestMatch = this.selectBestPartMatch(parts, modification);
            return {
                specificPart: bestMatch.name,
                brand: bestMatch.brand || 'OEM Style',
                partNumber: bestMatch.partNumber || undefined,
                price: Number(bestMatch.averagePrice),
                averagePrice: Number(bestMatch.averagePrice),
                installationTime: bestMatch.installationTime,
                description: bestMatch.description,
                availability: bestMatch.availability,
                deliveryDays: Math.floor((bestMatch.deliveryDaysMin + bestMatch.deliveryDaysMax) / 2),
                warranty: `${bestMatch.warrantyMonths} месяцев`,
                material: bestMatch.material || undefined,
                origin: bestMatch.origin,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting pricing from database:', error);
            return null;
        }
    }
    selectBestPartMatch(parts, modification) {
        const modLower = modification.toLowerCase();
        let bestMatch = parts.find(part => part.name.toLowerCase().includes(modLower));
        if (!bestMatch) {
            bestMatch = parts.find(part => part.description.toLowerCase().includes(modLower));
        }
        return bestMatch || parts[0];
    }
    getLegacyPricingFromDatabase(key, modification) {
        const mod = modification.toLowerCase();
        if (mod.includes('body kit') || mod.includes('bodykit')) {
            return {
                specificPart: 'Complete Sport Body Kit',
                brand: 'Maxton Design',
                partNumber: 'BD-' + Math.random().toString(36).substr(2, 6).toUpperCase(),
                price: this.randomPrice(800, 2500),
                averagePrice: 1500,
                installationTime: '6-8 hours',
                description: 'Complete aerodynamic body kit including front splitter, side skirts, and rear diffuser',
            };
        }
        if (mod.includes('wheels') || mod.includes('rims')) {
            const isBlack = mod.includes('black');
            const basePrice = this.randomPrice(600, 1800);
            return {
                specificPart: isBlack ? 'Black Sport Alloy Wheels (Set of 4)' : 'Sport Alloy Wheels (Set of 4)',
                brand: 'BBS',
                partNumber: 'CH-R' + Math.random().toString(36).substr(2, 4).toUpperCase(),
                price: basePrice,
                averagePrice: 1200,
                installationTime: '1-2 hours',
                description: `High-quality ${isBlack ? 'matte black' : 'silver'} alloy wheels with performance tires`,
            };
        }
        if (mod.includes('spoiler') || mod.includes('wing')) {
            return {
                specificPart: 'Rear Spoiler',
                brand: 'Vorsteiner',
                partNumber: 'SP-' + Math.random().toString(36).substr(2, 5).toUpperCase(),
                price: this.randomPrice(300, 800),
                averagePrice: 500,
                installationTime: '2-3 hours',
                description: 'Carbon fiber rear spoiler with aggressive styling',
            };
        }
        if (mod.includes('suspension') || mod.includes('lower')) {
            return {
                specificPart: 'Lowering Springs Kit',
                brand: 'Eibach',
                partNumber: 'PRO-KIT-' + Math.random().toString(36).substr(2, 4).toUpperCase(),
                price: this.randomPrice(400, 1200),
                averagePrice: 700,
                installationTime: '4-6 hours',
                description: 'Progressive rate lowering springs for improved handling and stance',
            };
        }
        if (mod.includes('exhaust')) {
            return {
                specificPart: 'Cat-Back Exhaust System',
                brand: 'Borla',
                partNumber: 'ATAK-' + Math.random().toString(36).substr(2, 5).toUpperCase(),
                price: this.randomPrice(600, 1500),
                averagePrice: 900,
                installationTime: '2-4 hours',
                description: 'Stainless steel cat-back exhaust system with aggressive sound',
            };
        }
        if (mod.includes('splitter')) {
            return {
                specificPart: 'Front Splitter',
                brand: 'APR Performance',
                partNumber: 'FS-' + Math.random().toString(36).substr(2, 6).toUpperCase(),
                price: this.randomPrice(200, 600),
                averagePrice: 350,
                installationTime: '1-2 hours',
                description: 'Carbon fiber front splitter for enhanced aerodynamics',
            };
        }
        return this.getDefaultPricing(modification);
    }
    getDefaultPricing(modification) {
        return {
            specificPart: this.formatModificationName(modification),
            brand: 'OEM Style',
            price: this.randomPrice(200, 800),
            averagePrice: 500,
            installationTime: '2-4 hours',
            description: `Quality aftermarket ${modification.toLowerCase()} modification`,
        };
    }
    formatModificationName(modification) {
        return modification
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
    randomPrice(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    async getExternalModificationPricing(carInfo, modification) {
        const cacheKey = `${carInfo.make}_${carInfo.model}_${modification}`;
        const cached = this.getCachedPrice(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const abcpResult = await this.searchInABCP(carInfo, modification);
            if (abcpResult) {
                this.setCachedPrice(cacheKey, abcpResult);
                return abcpResult;
            }
            const existResult = await this.searchInExist(carInfo, modification);
            if (existResult) {
                this.setCachedPrice(cacheKey, existResult);
                return existResult;
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error getting external pricing:', error);
            return null;
        }
    }
    async searchInABCP(carInfo, modification) {
        try {
            const searchQuery = this.buildSearchQuery(carInfo, modification);
            const result = await this.abcpService.searchParts({
                query: searchQuery,
                limit: 5,
            });
            if (result.parts.length > 0) {
                const bestPart = this.selectBestPart(result.parts);
                return this.convertAbcpPartToPricing(bestPart, modification);
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error searching in ABCP:', error);
            return null;
        }
    }
    async searchInExist(carInfo, modification) {
        try {
            const searchQuery = this.buildSearchQuery(carInfo, modification);
            const result = await this.existService.searchParts({
                query: searchQuery,
                limit: 5,
            });
            if (result.parts.length > 0) {
                const bestPart = this.selectBestExistPart(result.parts);
                return this.convertExistPartToPricing(bestPart, modification);
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error searching in Exist.ru:', error);
            return null;
        }
    }
    buildSearchQuery(carInfo, modification) {
        const mod = modification.toLowerCase();
        if (mod.includes('body kit')) {
            return `${carInfo.make} ${carInfo.model} body kit обвес`;
        }
        if (mod.includes('wheels') || mod.includes('rims')) {
            return `${carInfo.make} ${carInfo.model} диски колеса wheels`;
        }
        if (mod.includes('spoiler')) {
            return `${carInfo.make} ${carInfo.model} spoiler спойлер`;
        }
        if (mod.includes('suspension')) {
            return `${carInfo.make} ${carInfo.model} suspension подвеска`;
        }
        if (mod.includes('exhaust')) {
            return `${carInfo.make} ${carInfo.model} exhaust выхлоп`;
        }
        return `${carInfo.make} ${carInfo.model} ${modification}`;
    }
    selectBestPart(parts) {
        return parts.sort((a, b) => {
            const scoreA = (a.availability > 0 ? 10 : 0) + (a.price < 10000 ? 5 : 0);
            const scoreB = (b.availability > 0 ? 10 : 0) + (b.price < 10000 ? 5 : 0);
            return scoreB - scoreA;
        })[0];
    }
    selectBestExistPart(parts) {
        return parts.sort((a, b) => {
            const scoreA = (a.availability === 'in_stock' ? 10 : a.availability === 'order' ? 5 : 0);
            const scoreB = (b.availability === 'in_stock' ? 10 : b.availability === 'order' ? 5 : 0);
            return scoreB - scoreA;
        })[0];
    }
    convertAbcpPartToPricing(part, modification) {
        return {
            specificPart: part.name,
            brand: part.brand,
            partNumber: part.number,
            price: part.price,
            averagePrice: part.price,
            installationTime: this.estimateInstallationTime(modification),
            description: `${part.brand} ${part.name} - Available: ${part.availability} pcs, Delivery: ${part.deliveryDays} days`,
        };
    }
    convertExistPartToPricing(part, modification) {
        return {
            specificPart: part.name,
            brand: part.brand,
            partNumber: part.partNumber,
            price: part.price,
            averagePrice: part.price,
            installationTime: this.estimateInstallationTime(modification),
            description: `${part.brand} ${part.name} - Status: ${part.availability}, Delivery: ${part.deliveryDays} days`,
        };
    }
    estimateInstallationTime(modification) {
        const mod = modification.toLowerCase();
        if (mod.includes('body kit'))
            return '6-8 hours';
        if (mod.includes('wheels'))
            return '1-2 hours';
        if (mod.includes('spoiler'))
            return '2-3 hours';
        if (mod.includes('suspension'))
            return '4-6 hours';
        if (mod.includes('exhaust'))
            return '2-4 hours';
        if (mod.includes('splitter'))
            return '1-2 hours';
        return '2-4 hours';
    }
    getCachedPrice(key) {
        const cached = this.priceCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
            return cached.data;
        }
        return null;
    }
    setCachedPrice(key, data) {
        this.priceCache.set(key, {
            data,
            timestamp: Date.now(),
        });
    }
    initializePriceDatabase() {
        logger_1.logger.info('Price database initialized');
    }
    async getExternalPricing(carInfo, partNumber) {
        try {
            if (config_1.config.AUTODOC_API_KEY) {
                const autodocPrice = await this.getAutodocPricing(carInfo, partNumber);
                if (autodocPrice)
                    return autodocPrice;
            }
            if (config_1.config.EXIST_API_KEY) {
                const existPrice = await this.getExistPricing(carInfo, partNumber);
                if (existPrice)
                    return existPrice;
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error fetching external pricing:', error);
            return null;
        }
    }
    async getAutodocPricing(carInfo, partNumber) {
        try {
            const response = await axios_1.default.get(`https://api.autodoc.com/v1/parts/search`, {
                headers: {
                    'Authorization': `Bearer ${config_1.config.AUTODOC_API_KEY}`,
                    'Content-Type': 'application/json',
                },
                params: {
                    make: carInfo.make,
                    model: carInfo.model,
                    year: carInfo.year,
                    part_number: partNumber,
                },
                timeout: 5000,
            });
            if (response.data && response.data.parts && response.data.parts.length > 0) {
                const part = response.data.parts[0];
                return {
                    specificPart: part.name,
                    brand: part.brand,
                    partNumber: part.part_number,
                    price: parseFloat(part.price),
                    averagePrice: parseFloat(part.price),
                    installationTime: '2-4 hours',
                    description: part.description || `${part.brand} ${part.name}`,
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error fetching AutoDoc pricing:', error);
            return null;
        }
    }
    async getExistPricing(carInfo, partNumber) {
        try {
            const response = await axios_1.default.post('https://api.exist.ru/api/search', {
                login: 'your_login',
                password: 'your_password',
                search_string: partNumber,
                car_make: carInfo.make,
                car_model: carInfo.model,
                car_year: carInfo.year,
            }, {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 5000,
            });
            if (response.data && response.data.parts && response.data.parts.length > 0) {
                const part = response.data.parts[0];
                return {
                    specificPart: part.name,
                    brand: part.brand,
                    partNumber: part.code,
                    price: parseFloat(part.price),
                    averagePrice: parseFloat(part.price),
                    installationTime: '2-4 hours',
                    description: part.description || `${part.brand} ${part.name}`,
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error('Error fetching Exist.ru pricing:', error);
            return null;
        }
    }
    async getPriceComparison(carInfo, modification) {
        const basePricing = await this.getModificationPricing(carInfo, modification);
        const variations = [
            basePricing,
            {
                ...basePricing,
                brand: 'OEM',
                price: Math.round(basePricing.price * 1.3),
                description: `OEM ${basePricing.specificPart}`,
            },
            {
                ...basePricing,
                brand: 'Budget Option',
                price: Math.round(basePricing.price * 0.7),
                description: `Budget ${basePricing.specificPart}`,
            },
        ];
        return variations;
    }
}
exports.PricingService = PricingService;
//# sourceMappingURL=pricing.service.js.map