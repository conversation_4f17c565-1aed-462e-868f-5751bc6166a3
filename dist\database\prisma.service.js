"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prismaService = exports.PrismaService = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("@/utils/logger");
class PrismaService {
    constructor() {
        this.prisma = new client_1.PrismaClient({
            log: [
                { level: 'query', emit: 'event' },
                { level: 'error', emit: 'stdout' },
                { level: 'info', emit: 'stdout' },
                { level: 'warn', emit: 'stdout' },
            ],
        });
        if (process.env.NODE_ENV === 'development') {
        }
    }
    static getInstance() {
        if (!PrismaService.instance) {
            PrismaService.instance = new PrismaService();
        }
        return PrismaService.instance;
    }
    getClient() {
        return this.prisma;
    }
    async connect() {
        try {
            await this.prisma.$connect();
            logger_1.logger.info('Database connected successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to connect to database:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            await this.prisma.$disconnect();
            logger_1.logger.info('Database disconnected successfully');
        }
        catch (error) {
            logger_1.logger.error('Error disconnecting from database:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            await this.prisma.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            logger_1.logger.error('Database health check failed:', error);
            return false;
        }
    }
    async transaction(fn) {
        return await this.prisma.$transaction(fn);
    }
    async getStats() {
        try {
            const [userCount, carCount, modificationCount, sessionCount,] = await Promise.all([
                this.prisma.user.count(),
                this.prisma.car.count(),
                this.prisma.modificationPart.count(),
                this.prisma.userSession.count(),
            ]);
            return {
                users: userCount,
                cars: carCount,
                modifications: modificationCount,
                activeSessions: sessionCount,
            };
        }
        catch (error) {
            logger_1.logger.error('Error getting database stats:', error);
            return null;
        }
    }
    async cleanup() {
        try {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            const deletedSessions = await this.prisma.userSession.deleteMany({
                where: {
                    lastActivity: {
                        lt: oneWeekAgo,
                    },
                },
            });
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const deletedApiUsage = await this.prisma.apiUsage.deleteMany({
                where: {
                    createdAt: {
                        lt: thirtyDaysAgo,
                    },
                },
            });
            logger_1.logger.info('Database cleanup completed', {
                deletedSessions: deletedSessions.count,
                deletedApiUsage: deletedApiUsage.count,
            });
        }
        catch (error) {
            logger_1.logger.error('Error during database cleanup:', error);
        }
    }
}
exports.PrismaService = PrismaService;
exports.prismaService = PrismaService.getInstance();
//# sourceMappingURL=prisma.service.js.map