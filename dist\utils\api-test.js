"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiTestUtils = exports.APITestUtils = void 0;
const proxyapi_service_1 = require("@/services/proxyapi.service");
const openrouter_service_1 = require("@/services/openrouter.service");
const logger_1 = require("@/utils/logger");
class APITestUtils {
    constructor() {
        this.proxyAPIService = new proxyapi_service_1.ProxyAPIService();
        this.openRouterService = new openrouter_service_1.OpenRouterService();
    }
    async testAllConnections() {
        logger_1.logger.info('Testing API connections...');
        const proxyAPIResult = await this.testProxyAPI();
        const openRouterResult = await this.testOpenRouter();
        const overall = proxyAPIResult && openRouterResult;
        logger_1.logger.info('API connection test results:', {
            proxyAPI: proxyAPIResult,
            openRouter: openRouterResult,
            overall,
        });
        return {
            proxyAPI: proxyAPIResult,
            openRouter: openRouterResult,
            overall,
        };
    }
    async testProxyAPI() {
        try {
            logger_1.logger.info('Testing ProxyAPI connection...');
            const result = await this.proxyAPIService.testConnection();
            logger_1.logger.info(`ProxyAPI test result: ${result ? 'SUCCESS' : 'FAILED'}`);
            return result;
        }
        catch (error) {
            logger_1.logger.error('ProxyAPI test failed:', error);
            return false;
        }
    }
    async testOpenRouter() {
        try {
            logger_1.logger.info('Testing OpenRouter connection...');
            const result = await this.openRouterService.testConnection();
            logger_1.logger.info(`OpenRouter test result: ${result ? 'SUCCESS' : 'FAILED'}`);
            return result;
        }
        catch (error) {
            logger_1.logger.error('OpenRouter test failed:', error);
            return false;
        }
    }
    async testCarRecognition(imageUrl) {
        try {
            logger_1.logger.info('Testing car recognition...');
            const result = await this.openRouterService.recognizeCar(imageUrl);
            if (result && result.carInfo && result.carInfo.make && result.carInfo.model) {
                logger_1.logger.info('Car recognition test SUCCESS:', {
                    make: result.carInfo.make,
                    model: result.carInfo.model,
                    year: result.carInfo.year,
                    confidence: result.confidence,
                });
                return true;
            }
            else {
                logger_1.logger.warn('Car recognition test FAILED: Invalid response format');
                return false;
            }
        }
        catch (error) {
            logger_1.logger.error('Car recognition test failed:', error);
            return false;
        }
    }
    async testImageGeneration() {
        try {
            logger_1.logger.info('Testing image generation...');
            const testCarInfo = {
                id: 'test_car',
                make: 'BMW',
                model: '3 Series',
                year: 2020,
                bodyType: 'sedan',
                confidence: 0.9,
                detectedFeatures: ['headlights', 'grille', 'wheels'],
            };
            const result = await this.proxyAPIService.generateModifiedImage('https://example.com/test-car.jpg', testCarInfo, ['sport body kit', 'black wheels'], 'Make it look sporty');
            if (result && result.url) {
                logger_1.logger.info('Image generation test SUCCESS');
                return true;
            }
            else {
                logger_1.logger.warn('Image generation test FAILED: No image URL returned');
                return false;
            }
        }
        catch (error) {
            logger_1.logger.error('Image generation test failed:', error);
            return false;
        }
    }
    async runComprehensiveTests(testImageUrl) {
        logger_1.logger.info('Running comprehensive API tests...');
        const connections = await this.testAllConnections();
        const results = { connections };
        if (connections.overall) {
            if (testImageUrl) {
                results.carRecognition = await this.testCarRecognition(testImageUrl);
            }
            results.imageGeneration = await this.testImageGeneration();
        }
        results.overallSuccess = connections.overall &&
            (results.carRecognition !== false) &&
            (results.imageGeneration !== false);
        logger_1.logger.info('Comprehensive test results:', results);
        return results;
    }
}
exports.APITestUtils = APITestUtils;
exports.apiTestUtils = new APITestUtils();
//# sourceMappingURL=api-test.js.map