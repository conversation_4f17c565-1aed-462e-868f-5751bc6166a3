#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import axios from 'axios';
import { PrismaClient } from '@prisma/client';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';

// Load environment variables
dotenv.config();

interface HealthCheckResult {
  service: string;
  status: 'OK' | 'ERROR' | 'WARNING';
  message: string;
  details?: any;
}

class HealthChecker {
  private results: HealthCheckResult[] = [];
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  private addResult(service: string, status: 'OK' | 'ERROR' | 'WARNING', message: string, details?: any) {
    this.results.push({ service, status, message, details });
  }

  private log(emoji: string, message: string) {
    console.log(`${emoji} ${message}`);
  }

  async checkEnvironmentVariables() {
    this.log('🔍', 'Проверка переменных окружения...');

    const required = [
      'TELEGRAM_BOT_TOKEN',
      'PROXYAPI_KEY', 
      'OPENROUTER_API_KEY',
      'DATABASE_URL'
    ];

    const optional = [
      'ABCP_API_KEY',
      'EXIST_API_KEY',
      'AUTODOC_API_KEY'
    ];

    let missingRequired = [];
    let missingOptional = [];

    for (const key of required) {
      if (!process.env[key]) {
        missingRequired.push(key);
      }
    }

    for (const key of optional) {
      if (!process.env[key]) {
        missingOptional.push(key);
      }
    }

    if (missingRequired.length > 0) {
      this.addResult(
        'Environment Variables',
        'ERROR',
        `Отсутствуют обязательные переменные: ${missingRequired.join(', ')}`
      );
    } else {
      this.addResult(
        'Environment Variables',
        'OK',
        'Все обязательные переменные настроены'
      );
    }

    if (missingOptional.length > 0) {
      this.addResult(
        'Optional APIs',
        'WARNING',
        `Опциональные API не настроены: ${missingOptional.join(', ')}`
      );
    }
  }

  async checkDirectories() {
    this.log('📁', 'Проверка директорий...');

    const directories = [
      process.env.UPLOAD_DIR || './uploads',
      './logs',
      './dist'
    ];

    for (const dir of directories) {
      if (!existsSync(dir)) {
        try {
          mkdirSync(dir, { recursive: true });
          this.addResult(
            'Directories',
            'OK',
            `Создана директория: ${dir}`
          );
        } catch (error) {
          this.addResult(
            'Directories',
            'ERROR',
            `Не удалось создать директорию: ${dir}`,
            error
          );
        }
      } else {
        this.addResult(
          'Directories',
          'OK',
          `Директория существует: ${dir}`
        );
      }
    }
  }

  async checkDatabase() {
    this.log('🗄️', 'Проверка базы данных...');

    try {
      await this.prisma.$connect();
      
      // Try to query a table
      const userCount = await this.prisma.user.count();
      
      this.addResult(
        'Database',
        'OK',
        `Подключение к БД успешно. Пользователей: ${userCount}`
      );
    } catch (error) {
      this.addResult(
        'Database',
        'ERROR',
        'Не удалось подключиться к базе данных',
        error instanceof Error ? error.message : error
      );
    } finally {
      await this.prisma.$disconnect();
    }
  }

  async checkTelegramAPI() {
    this.log('📱', 'Проверка Telegram API...');

    if (!process.env.TELEGRAM_BOT_TOKEN) {
      this.addResult('Telegram API', 'ERROR', 'TELEGRAM_BOT_TOKEN не настроен');
      return;
    }

    try {
      const response = await axios.get(
        `https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/getMe`,
        { timeout: 10000 }
      );

      if (response.data.ok) {
        this.addResult(
          'Telegram API',
          'OK',
          `Бот активен: @${response.data.result.username}`
        );
      } else {
        this.addResult(
          'Telegram API',
          'ERROR',
          'Неверный токен Telegram бота'
        );
      }
    } catch (error) {
      this.addResult(
        'Telegram API',
        'ERROR',
        'Ошибка подключения к Telegram API',
        error instanceof Error ? error.message : error
      );
    }
  }

  async checkProxyAPI() {
    this.log('🎨', 'Проверка ProxyAPI...');

    if (!process.env.PROXYAPI_KEY) {
      this.addResult('ProxyAPI', 'ERROR', 'PROXYAPI_KEY не настроен');
      return;
    }

    try {
      const response = await axios.get(
        'https://api.proxyapi.ru/openai/v1/models',
        {
          headers: {
            'Authorization': `Bearer ${process.env.PROXYAPI_KEY}`,
          },
          timeout: 10000,
        }
      );

      const hasImageModel = response.data.data?.some((model: any) => 
        model.id === 'gpt-image-1'
      );

      if (hasImageModel) {
        this.addResult(
          'ProxyAPI',
          'OK',
          'ProxyAPI доступен, модель gpt-image-1 найдена'
        );
      } else {
        this.addResult(
          'ProxyAPI',
          'WARNING',
          'ProxyAPI доступен, но модель gpt-image-1 не найдена'
        );
      }
    } catch (error) {
      this.addResult(
        'ProxyAPI',
        'ERROR',
        'Ошибка подключения к ProxyAPI',
        error instanceof Error ? error.message : error
      );
    }
  }

  async checkOpenRouter() {
    this.log('🧠', 'Проверка OpenRouter...');

    if (!process.env.OPENROUTER_API_KEY) {
      this.addResult('OpenRouter', 'ERROR', 'OPENROUTER_API_KEY не настроен');
      return;
    }

    try {
      const response = await axios.get(
        'https://openrouter.ai/api/v1/models',
        {
          headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          },
          timeout: 10000,
        }
      );

      const hasVisionModel = response.data.data?.some((model: any) => 
        model.id === 'openai/gpt-4o'
      );

      if (hasVisionModel) {
        this.addResult(
          'OpenRouter',
          'OK',
          'OpenRouter доступен, модель gpt-4o найдена'
        );
      } else {
        this.addResult(
          'OpenRouter',
          'WARNING',
          'OpenRouter доступен, но модель gpt-4o не найдена'
        );
      }
    } catch (error) {
      this.addResult(
        'OpenRouter',
        'ERROR',
        'Ошибка подключения к OpenRouter',
        error instanceof Error ? error.message : error
      );
    }
  }

  async checkABCP() {
    this.log('🔧', 'Проверка ABCP API...');

    if (!process.env.ABCP_API_KEY) {
      this.addResult('ABCP API', 'WARNING', 'ABCP_API_KEY не настроен (опционально)');
      return;
    }

    try {
      // Note: This is a hypothetical endpoint, actual ABCP API may differ
      const response = await axios.get(
        'https://api.abcp.ru/health',
        {
          headers: {
            'Authorization': `Bearer ${process.env.ABCP_API_KEY}`,
          },
          timeout: 10000,
        }
      );

      this.addResult(
        'ABCP API',
        'OK',
        'ABCP API доступен'
      );
    } catch (error) {
      this.addResult(
        'ABCP API',
        'WARNING',
        'ABCP API недоступен или неверный ключ',
        error instanceof Error ? error.message : error
      );
    }
  }

  async runAllChecks() {
    console.log('🚀 Запуск проверки здоровья PimpMyRideAI...\n');

    await this.checkEnvironmentVariables();
    await this.checkDirectories();
    await this.checkDatabase();
    await this.checkTelegramAPI();
    await this.checkProxyAPI();
    await this.checkOpenRouter();
    await this.checkABCP();

    this.printResults();
  }

  private printResults() {
    console.log('\n📊 Результаты проверки:\n');

    let errorCount = 0;
    let warningCount = 0;
    let okCount = 0;

    for (const result of this.results) {
      const emoji = result.status === 'OK' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`${emoji} ${result.service}: ${result.message}`);
      
      if (result.details) {
        console.log(`   Детали: ${result.details}`);
      }

      if (result.status === 'ERROR') errorCount++;
      else if (result.status === 'WARNING') warningCount++;
      else okCount++;
    }

    console.log('\n📈 Сводка:');
    console.log(`✅ Успешно: ${okCount}`);
    console.log(`⚠️ Предупреждения: ${warningCount}`);
    console.log(`❌ Ошибки: ${errorCount}`);

    if (errorCount === 0) {
      console.log('\n🎉 Все основные компоненты работают! Бот готов к запуску.');
    } else {
      console.log('\n🔧 Исправьте ошибки перед запуском бота.');
      process.exit(1);
    }
  }
}

// Run health check
async function main() {
  const checker = new HealthChecker();
  await checker.runAllChecks();
}

if (require.main === module) {
  main().catch(console.error);
}

export { HealthChecker };
