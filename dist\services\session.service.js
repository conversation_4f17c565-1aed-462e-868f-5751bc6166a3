"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = void 0;
const types_1 = require("@/types");
const logger_1 = require("@/utils/logger");
class SessionManager {
    constructor() {
        this.sessions = new Map();
        this.SESSION_TIMEOUT = 30 * 60 * 1000;
        setInterval(() => {
            this.cleanupExpiredSessions();
        }, 5 * 60 * 1000);
    }
    async createSession(userId, chatId) {
        const session = {
            userId,
            chatId,
            currentStep: types_1.ConversationStep.WAITING_FOR_IMAGE,
            lastActivity: new Date(),
        };
        this.sessions.set(userId, session);
        logger_1.logger.info(`Created session for user ${userId}`);
        return session;
    }
    async getSession(userId) {
        const session = this.sessions.get(userId);
        if (!session) {
            return null;
        }
        const now = new Date();
        const timeDiff = now.getTime() - session.lastActivity.getTime();
        if (timeDiff > this.SESSION_TIMEOUT) {
            this.sessions.delete(userId);
            logger_1.logger.info(`Session expired for user ${userId}`);
            return null;
        }
        session.lastActivity = now;
        return session;
    }
    async updateSession(userId, updates) {
        const session = await this.getSession(userId);
        if (!session) {
            logger_1.logger.warn(`Attempted to update non-existent session for user ${userId}`);
            return null;
        }
        Object.assign(session, updates, { lastActivity: new Date() });
        this.sessions.set(userId, session);
        logger_1.logger.debug(`Updated session for user ${userId}`, updates);
        return session;
    }
    async deleteSession(userId) {
        const deleted = this.sessions.delete(userId);
        if (deleted) {
            logger_1.logger.info(`Deleted session for user ${userId}`);
        }
        return deleted;
    }
    async getActiveSessions() {
        return Array.from(this.sessions.values());
    }
    cleanupExpiredSessions() {
        const now = new Date();
        let cleanedCount = 0;
        for (const [userId, session] of this.sessions.entries()) {
            const timeDiff = now.getTime() - session.lastActivity.getTime();
            if (timeDiff > this.SESSION_TIMEOUT) {
                this.sessions.delete(userId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            logger_1.logger.info(`Cleaned up ${cleanedCount} expired sessions`);
        }
    }
    async getSessionStats() {
        const sessions = Array.from(this.sessions.values());
        const byStep = {
            [types_1.ConversationStep.WAITING_FOR_IMAGE]: 0,
            [types_1.ConversationStep.PROCESSING_IMAGE]: 0,
            [types_1.ConversationStep.CAR_RECOGNIZED]: 0,
            [types_1.ConversationStep.WAITING_FOR_MODIFICATIONS]: 0,
            [types_1.ConversationStep.GENERATING_MODIFICATIONS]: 0,
            [types_1.ConversationStep.SHOWING_RESULTS]: 0,
        };
        sessions.forEach(session => {
            byStep[session.currentStep]++;
        });
        return {
            total: sessions.length,
            byStep,
        };
    }
}
exports.SessionManager = SessionManager;
//# sourceMappingURL=session.service.js.map