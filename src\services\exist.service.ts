import axios, { AxiosInstance } from 'axios';
import { config } from '@/config';
import { logger } from '@/utils/logger';

export interface ExistPartInfo {
  id: string;
  brand: string;
  partNumber: string;
  name: string;
  price: number;
  currency: string;
  availability: 'in_stock' | 'order' | 'out_of_stock';
  deliveryDays: number;
  description?: string;
  imageUrl?: string;
  weight?: number;
  oem?: string[];
}

export interface ExistSearchParams {
  query: string;
  brand?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  limit?: number;
}

export interface ExistSearchResponse {
  parts: ExistPartInfo[];
  total: number;
  categories: string[];
}

/**
 * Service for integration with Exist.ru API
 * Note: This is a conceptual implementation as Exist.ru API details may vary
 */
export class ExistService {
  private client: AxiosInstance;
  private readonly baseURL = 'https://api.exist.ru'; // Hypothetical API endpoint

  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PimpMyRideAI/1.0',
      },
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      if (process.env.EXIST_API_KEY) {
        config.headers['X-API-Key'] = process.env.EXIST_API_KEY;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error('Exist.ru API Error:', {
          status: error.response?.status,
          message: error.response?.data?.message || error.message,
          url: error.config?.url,
        });
        throw error;
      }
    );
  }

  /**
   * Search for parts by query
   */
  async searchParts(params: ExistSearchParams): Promise<ExistSearchResponse> {
    try {
      logger.info(`Searching Exist.ru for: ${params.query}`);

      const response = await this.client.get('/v1/parts/search', {
        params: {
          q: params.query,
          brand: params.brand,
          category: params.category,
          min_price: params.minPrice,
          max_price: params.maxPrice,
          limit: params.limit || 20,
        },
      });

      const parts: ExistPartInfo[] = response.data.results.map((item: any) => ({
        id: item.id,
        brand: item.brand,
        partNumber: item.part_number,
        name: item.name,
        price: parseFloat(item.price),
        currency: item.currency || 'RUB',
        availability: this.mapAvailability(item.availability),
        deliveryDays: item.delivery_days || 0,
        description: item.description,
        imageUrl: item.image_url,
        weight: item.weight,
        oem: item.oem_numbers || [],
      }));

      return {
        parts,
        total: response.data.total,
        categories: response.data.categories || [],
      };
    } catch (error) {
      logger.error('Error searching Exist.ru parts:', error);
      throw new Error('Failed to search parts in Exist.ru');
    }
  }

  /**
   * Get part details by ID
   */
  async getPartDetails(partId: string): Promise<ExistPartInfo | null> {
    try {
      const response = await this.client.get(`/v1/parts/${partId}`);
      
      return {
        id: response.data.id,
        brand: response.data.brand,
        partNumber: response.data.part_number,
        name: response.data.name,
        price: parseFloat(response.data.price),
        currency: response.data.currency || 'RUB',
        availability: this.mapAvailability(response.data.availability),
        deliveryDays: response.data.delivery_days || 0,
        description: response.data.description,
        imageUrl: response.data.image_url,
        weight: response.data.weight,
        oem: response.data.oem_numbers || [],
      };
    } catch (error) {
      logger.error(`Error getting Exist.ru part details for ${partId}:`, error);
      return null;
    }
  }

  /**
   * Search by OEM number
   */
  async searchByOEM(oemNumber: string): Promise<ExistPartInfo[]> {
    try {
      const response = await this.client.get('/v1/parts/by-oem', {
        params: {
          oem: oemNumber,
        },
      });

      return response.data.results.map((item: any) => ({
        id: item.id,
        brand: item.brand,
        partNumber: item.part_number,
        name: item.name,
        price: parseFloat(item.price),
        currency: item.currency || 'RUB',
        availability: this.mapAvailability(item.availability),
        deliveryDays: item.delivery_days || 0,
        description: item.description,
        imageUrl: item.image_url,
        weight: item.weight,
        oem: item.oem_numbers || [],
      }));
    } catch (error) {
      logger.error(`Error searching by OEM ${oemNumber}:`, error);
      return [];
    }
  }

  /**
   * Get categories for a specific car
   */
  async getCarCategories(make: string, model: string, year: number): Promise<string[]> {
    try {
      const response = await this.client.get('/v1/categories', {
        params: {
          make,
          model,
          year,
        },
      });

      return response.data.categories || [];
    } catch (error) {
      logger.error(`Error getting categories for ${make} ${model} ${year}:`, error);
      return [];
    }
  }

  /**
   * Check if API is available
   */
  async checkHealth(): Promise<boolean> {
    try {
      await this.client.get('/v1/health');
      return true;
    } catch (error) {
      logger.warn('Exist.ru API health check failed:', error);
      return false;
    }
  }

  /**
   * Map availability status from API response
   */
  private mapAvailability(status: any): 'in_stock' | 'order' | 'out_of_stock' {
    if (typeof status === 'string') {
      switch (status.toLowerCase()) {
        case 'in_stock':
        case 'available':
        case 'в наличии':
          return 'in_stock';
        case 'order':
        case 'под заказ':
          return 'order';
        default:
          return 'out_of_stock';
      }
    }
    
    if (typeof status === 'number') {
      return status > 0 ? 'in_stock' : 'out_of_stock';
    }
    
    return 'out_of_stock';
  }
}
