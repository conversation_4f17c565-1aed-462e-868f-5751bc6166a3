"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbcpService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("@/utils/logger");
class AbcpService {
    constructor() {
        this.baseURL = 'https://api.abcp.ru';
        this.client = axios_1.default.create({
            baseURL: this.baseURL,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'PimpMyRideAI/1.0',
            },
        });
        this.client.interceptors.request.use((config) => {
            if (process.env.ABCP_API_KEY) {
                config.headers['Authorization'] = `Bearer ${process.env.ABCP_API_KEY}`;
            }
            return config;
        });
        this.client.interceptors.response.use((response) => response, (error) => {
            logger_1.logger.error('ABCP API Error:', {
                status: error.response?.status,
                message: error.response?.data?.message || error.message,
                url: error.config?.url,
            });
            throw error;
        });
    }
    async searchParts(params) {
        try {
            logger_1.logger.info(`Searching ABCP for: ${params.query}`);
            const response = await this.client.get('/search/parts', {
                params: {
                    q: params.query,
                    brand: params.brand,
                    limit: params.limit || 20,
                    offset: params.offset || 0,
                },
            });
            const parts = response.data.items.map((item) => ({
                id: item.id,
                brand: item.brand,
                number: item.number,
                name: item.name,
                price: parseFloat(item.price),
                currency: item.currency || 'RUB',
                availability: item.availability || 0,
                deliveryDays: item.deliveryDays || 0,
                supplier: item.supplier,
                weight: item.weight,
                imageUrl: item.imageUrl,
            }));
            return {
                parts,
                total: response.data.total,
                hasMore: response.data.hasMore,
            };
        }
        catch (error) {
            logger_1.logger.error('Error searching ABCP parts:', error);
            throw new Error('Failed to search parts in ABCP');
        }
    }
    async getPartDetails(partId) {
        try {
            const response = await this.client.get(`/parts/${partId}`);
            return {
                id: response.data.id,
                brand: response.data.brand,
                number: response.data.number,
                name: response.data.name,
                price: parseFloat(response.data.price),
                currency: response.data.currency || 'RUB',
                availability: response.data.availability || 0,
                deliveryDays: response.data.deliveryDays || 0,
                supplier: response.data.supplier,
                weight: response.data.weight,
                imageUrl: response.data.imageUrl,
            };
        }
        catch (error) {
            logger_1.logger.error(`Error getting ABCP part details for ${partId}:`, error);
            return null;
        }
    }
    async findAnalogs(brand, partNumber) {
        try {
            const response = await this.client.get('/parts/analogs', {
                params: {
                    brand,
                    number: partNumber,
                },
            });
            return response.data.items.map((item) => ({
                id: item.id,
                brand: item.brand,
                number: item.number,
                name: item.name,
                price: parseFloat(item.price),
                currency: item.currency || 'RUB',
                availability: item.availability || 0,
                deliveryDays: item.deliveryDays || 0,
                supplier: item.supplier,
                weight: item.weight,
                imageUrl: item.imageUrl,
            }));
        }
        catch (error) {
            logger_1.logger.error(`Error finding analogs for ${brand} ${partNumber}:`, error);
            return [];
        }
    }
    async checkHealth() {
        try {
            await this.client.get('/health');
            return true;
        }
        catch (error) {
            logger_1.logger.warn('ABCP API health check failed:', error);
            return false;
        }
    }
}
exports.AbcpService = AbcpService;
//# sourceMappingURL=abcp.service.js.map