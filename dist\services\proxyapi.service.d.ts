import { CarInfo, OpenAIImageResponse } from '../types/index';
export declare class ProxyAPIService {
    private client;
    constructor();
    generateModifiedImage(originalImageUrl: string, carInfo: CarInfo, modifications: string[], userRequest: string): Promise<OpenAIImageResponse>;
    private generateImageFromPrompt;
    private generateModificationPrompt;
    private determineOptimalResolution;
    private downloadImageAsBuffer;
    private generateImageEditPrompt;
    private extractSpecificColors;
    private generateColorEnforcementInstructions;
    private generateNaturalIntegrationInstructions;
    private getModificationSpecificIntegrationInstructions;
    private generatePositionLockInstructions;
    private generateColorPreservationInstructions;
    private detectModificationType;
    private validatePositionAndColorPreservation;
    private generateTargetingMask;
    private testNetworkConnectivity;
    private downloadWithPreCheck;
    checkBalance(): Promise<{
        balance: number;
        currency: string;
    } | null>;
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=proxyapi.service.d.ts.map