# Car Modification Generator Bot 🚗

A Telegram bot that uses AI to generate realistic car modification visualizations with cost estimates and installation guidance.

## Features

- 🔍 **Car Recognition**: Automatically identifies car make, model, and year from photos using GPT-4o Vision via OpenRouter.ai
- 🎨 **AI Visualization**: Generates realistic modification previews using gpt-image-1 via ProxyAPI.ru
- 💰 **Cost Estimation**: Provides detailed pricing for parts and labor
- 🔧 **Installation Guidance**: Offers complexity ratings and professional recommendations
- 📱 **Telegram Integration**: Easy-to-use interface through Telegram bot

## Supported Modifications

- Body kits and aerodynamic packages
- Wheels and rims (various styles and colors)
- Spoilers and wings
- Suspension lowering
- Exhaust systems
- Front splitters and side skirts
- Custom styling packages

## Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Telegram Bot Token (from @BotFather)
- ProxyAPI.ru API Key (for image generation)
- OpenRouter.ai API Key (for image analysis)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd car-modification-generator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:generate
   ```

5. **Build and start**
   ```bash
   npm run build
   npm start
   ```

   For development:
   ```bash
   npm run dev
   ```

## Environment Configuration

Create a `.env` file with the following variables:

```env
# Required
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
PROXYAPI_KEY=your_proxyapi_key
OPENROUTER_API_KEY=your_openrouter_api_key
DATABASE_URL=postgresql://username:password@localhost:5432/car_modifications

# Optional
PROXYAPI_IMAGE_MODEL=gpt-image-1
OPENROUTER_MODEL_VISION=openai/gpt-4o
OPENROUTER_MODEL_TEXT=openai/gpt-4o-mini
PORT=3000
NODE_ENV=development
```

## Usage

1. **Start a conversation** with your bot on Telegram
2. **Send `/start`** to begin
3. **Upload a photo** of your car (side view recommended)
4. **Wait for recognition** - the bot will identify your car
5. **Describe modifications** you want (e.g., "Add a sport body kit and black wheels")
6. **Receive visualization** with cost breakdown and installation notes

### Example Commands

- `Add a sport body kit and lowered suspension`
- `I want black wheels and a rear spoiler`
- `Make it look more aggressive with a front splitter`
- `Add performance modifications`

## Architecture

```
src/
├── config/           # Configuration and environment setup
├── services/         # Core business logic services
│   ├── telegram.service.ts    # Telegram bot handling
│   ├── openai.service.ts      # Main AI service coordinator
│   ├── proxyapi.service.ts    # ProxyAPI.ru integration (image generation)
│   ├── openrouter.service.ts  # OpenRouter.ai integration (image analysis)
│   ├── modification.service.ts # Modification processing
│   ├── pricing.service.ts     # Cost calculation
│   └── session.service.ts     # User session management
├── types/            # TypeScript type definitions
├── utils/            # Utility functions and helpers
└── index.ts          # Application entry point
```

## API Integration

### AI Services Used

#### ProxyAPI.ru (Image Editing)
- **gpt-image-1**: Advanced image editing and modification
- **Base URL**: `https://api.proxyapi.ru/openai/v1`
- **Features**: High-quality car modification visualization with precise targeting

#### OpenRouter.ai (Image Analysis)
- **GPT-4o Vision**: Car recognition and analysis
- **GPT-4o Mini**: Text processing and analysis
- **Base URL**: `https://openrouter.ai/api/v1`
- **Features**: Accurate car identification and modification analysis

### External Pricing APIs (Planned)

- AutoDoc API for European parts
- Exist.ru for Russian market
- FCP Euro for BMW/Mercedes parts
- Pelican Parts for Porsche/Audi

## Development

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:studio` - Open Prisma Studio

### Database Schema

The application uses PostgreSQL with Prisma ORM. Key models:

- **User**: Telegram user information
- **Car**: Vehicle specifications and compatibility
- **ModificationPart**: Available modification parts with pricing
- **ModificationHistory**: User modification requests and results

## Deployment

### Docker Deployment (Recommended)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production

Ensure all required environment variables are set:
- Database connection with SSL
- OpenAI API key with sufficient credits
- Telegram bot token
- Proper logging configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details

## Roadmap

- [ ] Integration with real auto parts suppliers
- [ ] Support for more car makes and models
- [ ] Advanced modification combinations
- [ ] Price comparison from multiple suppliers
- [ ] User favorites and modification history
- [ ] Multi-language support
- [ ] Web interface for administration
