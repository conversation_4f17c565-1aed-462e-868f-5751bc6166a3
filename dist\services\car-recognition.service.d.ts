import { CarInfo, CarRecognitionResponse } from '@/types';
export declare class CarRecognitionService {
    private openAIService;
    private carDatabase;
    constructor();
    recognizeCar(imageUrl: string): Promise<CarRecognitionResponse>;
    private enhanceCarInfo;
    private generateEnhancedSuggestions;
    private calculateFinalConfidence;
    private normalizeMakeName;
    private normalizeModelName;
    private lookupCarInDatabase;
    private initializeCarDatabase;
    getCarSpecifications(carInfo: CarInfo): Promise<any>;
}
//# sourceMappingURL=car-recognition.service.d.ts.map