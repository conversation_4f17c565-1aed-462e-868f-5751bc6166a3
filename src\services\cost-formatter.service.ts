import { AppliedModification, CostBreakdown, DetailedPartInfo } from '@/types';
import { logger } from '@/utils/logger';

/**
 * Service for formatting cost information in Russian format
 */
export class CostFormatterService {
  
  /**
   * Format cost breakdown in the required Russian format
   */
  formatCostBreakdown(
    appliedModifications: AppliedModification[],
    totalCost: CostBreakdown
  ): string {
    try {
      const formattedParts = appliedModifications.map((mod, index) => 
        this.formatSingleModification(mod, index + 1)
      ).join('\n\n');

      const summary = this.formatCostSummary(totalCost);
      const notes = this.formatNotes();

      return `💰 Стоимость модификаций:

${formattedParts}

${summary}

${notes}`;
    } catch (error) {
      logger.error('Error formatting cost breakdown:', error);
      return this.getErrorMessage();
    }
  }

  /**
   * Format a single modification with detailed information
   */
  private formatSingleModification(modification: AppliedModification, index: number): string {
    const partInfo = this.convertToDetailedPartInfo(modification);
    
    return `${index}. ${partInfo.name}
   💵 Цена: ${this.formatPrice(partInfo.price)} ₽
   🏭 Бренд: ${partInfo.brand}
   ⏱️ Установка: ${partInfo.installationTime}
   📦 Наличие: ${partInfo.availability} ${this.getAvailabilityIcon(partInfo.availability)}
   🚚 Доставка: ${partInfo.deliveryDays} дн.
   🛡️ Гарантия: ${partInfo.warranty}`;
  }

  /**
   * Format cost summary section
   */
  private formatCostSummary(totalCost: CostBreakdown): string {
    return `📊 Итого:
💰 Запчасти: ${this.formatPrice(totalCost.parts)} ₽
🔧 Работы: ${this.formatPrice(totalCost.labor)} ₽
💎 Общая стоимость: ${this.formatPrice(totalCost.total)} ₽`;
  }

  /**
   * Format notes section
   */
  private formatNotes(): string {
    return `📝 Примечания:
• Цены указаны в российских рублях
• Стоимость работ может варьироваться в зависимости от региона
• Доставка рассчитывается отдельно`;
  }

  /**
   * Convert AppliedModification to DetailedPartInfo
   */
  private convertToDetailedPartInfo(modification: AppliedModification): DetailedPartInfo {
    return {
      name: modification.specificPart,
      brand: modification.brand,
      price: this.convertToRubles(modification.price),
      installationTime: modification.installationTime,
      availability: this.getRandomAvailability(),
      deliveryDays: this.getRandomDeliveryDays(),
      warranty: this.getWarrantyPeriod(modification.type.category),
      partNumber: modification.partNumber,
      description: modification.description
    };
  }

  /**
   * Convert USD price to RUB (approximate conversion)
   */
  private convertToRubles(usdPrice: number): number {
    const exchangeRate = 95; // Approximate USD to RUB rate
    return Math.round(usdPrice * exchangeRate);
  }

  /**
   * Format price with thousands separator
   */
  private formatPrice(price: number): string {
    return price.toLocaleString('ru-RU');
  }

  /**
   * Get availability icon
   */
  private getAvailabilityIcon(availability: string): string {
    switch (availability) {
      case 'В наличии':
        return '✅';
      case 'Под заказ':
        return '🔄';
      case 'Нет в наличии':
        return '❌';
      default:
        return '❓';
    }
  }

  /**
   * Get random availability status
   */
  private getRandomAvailability(): 'В наличии' | 'Под заказ' | 'Нет в наличии' {
    const statuses: ('В наличии' | 'Под заказ' | 'Нет в наличии')[] = [
      'В наличии', 'В наличии', 'В наличии', // 60% chance
      'Под заказ', 'Под заказ', // 40% chance
      'Нет в наличии' // 10% chance
    ];
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  /**
   * Get random delivery days
   */
  private getRandomDeliveryDays(): number {
    return Math.floor(Math.random() * 7) + 1; // 1-7 days
  }

  /**
   * Get warranty period based on modification category
   */
  private getWarrantyPeriod(category: string): string {
    const warranties: { [key: string]: string } = {
      'body_kit': '12 месяцев',
      'wheels': '24 месяца',
      'spoiler': '6 месяцев',
      'exhaust': '12 месяцев',
      'suspension': '24 месяца',
      'interior': '6 месяцев',
      'lighting': '12 месяцев',
      'performance': '6 месяцев'
    };
    
    return warranties[category] || '6 месяцев';
  }

  /**
   * Get error message when formatting fails
   */
  private getErrorMessage(): string {
    return `💰 Стоимость модификаций:

❌ Ошибка при расчете стоимости

📝 Примечания:
• Попробуйте повторить запрос
• Обратитесь к администратору если проблема повторяется`;
  }

  /**
   * Format simple cost summary for quick display
   */
  formatSimpleCostSummary(totalCost: CostBreakdown): string {
    return `💰 Общая стоимость: ${this.formatPrice(totalCost.total)} ₽
📊 Запчасти: ${this.formatPrice(totalCost.parts)} ₽ | Работы: ${this.formatPrice(totalCost.labor)} ₽`;
  }
}
