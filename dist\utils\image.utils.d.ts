import sharp from 'sharp';
export declare class ImageUtils {
    static downloadImage(imageUrl: string, filename: string): Promise<string>;
    static resizeImage(inputPath: string, outputPath: string, width: number, height: number): Promise<void>;
    static convertImage(inputPath: string, outputPath: string, format: 'jpeg' | 'png' | 'webp'): Promise<void>;
    static getImageMetadata(imagePath: string): Promise<sharp.Metadata>;
    static optimizeForWeb(inputPath: string, outputPath: string, maxWidth?: number): Promise<void>;
    static createThumbnail(inputPath: string, outputPath: string, size?: number): Promise<void>;
    static validateImage(imagePath: string): Promise<boolean>;
    static validateImageForEditing(imagePath: string): Promise<{
        isValid: boolean;
        errors: string[];
        metadata?: any;
    }>;
    static prepareForEditing(inputPath: string, outputPath: string, maxSize?: number, maxFileSize?: number): Promise<void>;
    static calculateOptimalDimensions(originalWidth: number, originalHeight: number, maxSize?: number): {
        targetWidth: number;
        targetHeight: number;
        resizeNeeded: boolean;
    };
    static downloadAndPrepareForEditing(imageUrl: string, outputPath: string, maxSize?: number, maxFileSize?: number): Promise<Buffer>;
    static cleanupOldFiles(directory: string, maxAgeHours?: number): Promise<void>;
    static generateFilename(prefix?: string, extension?: string): string;
    static generateCarPartMask(imagePath: string, outputPath: string, partType: 'wheels' | 'body' | 'windows' | 'full'): Promise<void>;
    static detectModificationType(userRequest: string): 'wheels' | 'body' | 'windows' | 'full';
    static validateImageQuality(imagePath: string): Promise<{
        isHighQuality: boolean;
        qualityScore: number;
        recommendations: string[];
    }>;
    static enhanceImageForModification(inputPath: string, outputPath: string): Promise<void>;
}
//# sourceMappingURL=image.utils.d.ts.map