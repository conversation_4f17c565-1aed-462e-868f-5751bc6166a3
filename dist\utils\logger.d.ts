import winston from 'winston';
export declare const logger: winston.Logger;
export declare const logRequest: (method: string, url: string, userId?: number, duration?: number) => void;
export declare const logError: (error: Error, context?: Record<string, any>) => void;
export declare const logPerformance: (operation: string, duration: number, metadata?: Record<string, any>) => void;
export declare const createModuleLogger: (module: string) => winston.Logger;
export default logger;
//# sourceMappingURL=logger.d.ts.map