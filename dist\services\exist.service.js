"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExistService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("@/utils/logger");
class ExistService {
    constructor() {
        this.baseURL = 'https://api.exist.ru';
        this.client = axios_1.default.create({
            baseURL: this.baseURL,
            timeout: 15000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'PimpMyRideAI/1.0',
            },
        });
        this.client.interceptors.request.use((config) => {
            if (process.env.EXIST_API_KEY) {
                config.headers['X-API-Key'] = process.env.EXIST_API_KEY;
            }
            return config;
        });
        this.client.interceptors.response.use((response) => response, (error) => {
            logger_1.logger.error('Exist.ru API Error:', {
                status: error.response?.status,
                message: error.response?.data?.message || error.message,
                url: error.config?.url,
            });
            throw error;
        });
    }
    async searchParts(params) {
        try {
            logger_1.logger.info(`Searching Exist.ru for: ${params.query}`);
            const response = await this.client.get('/v1/parts/search', {
                params: {
                    q: params.query,
                    brand: params.brand,
                    category: params.category,
                    min_price: params.minPrice,
                    max_price: params.maxPrice,
                    limit: params.limit || 20,
                },
            });
            const parts = response.data.results.map((item) => ({
                id: item.id,
                brand: item.brand,
                partNumber: item.part_number,
                name: item.name,
                price: parseFloat(item.price),
                currency: item.currency || 'RUB',
                availability: this.mapAvailability(item.availability),
                deliveryDays: item.delivery_days || 0,
                description: item.description,
                imageUrl: item.image_url,
                weight: item.weight,
                oem: item.oem_numbers || [],
            }));
            return {
                parts,
                total: response.data.total,
                categories: response.data.categories || [],
            };
        }
        catch (error) {
            logger_1.logger.error('Error searching Exist.ru parts:', error);
            throw new Error('Failed to search parts in Exist.ru');
        }
    }
    async getPartDetails(partId) {
        try {
            const response = await this.client.get(`/v1/parts/${partId}`);
            return {
                id: response.data.id,
                brand: response.data.brand,
                partNumber: response.data.part_number,
                name: response.data.name,
                price: parseFloat(response.data.price),
                currency: response.data.currency || 'RUB',
                availability: this.mapAvailability(response.data.availability),
                deliveryDays: response.data.delivery_days || 0,
                description: response.data.description,
                imageUrl: response.data.image_url,
                weight: response.data.weight,
                oem: response.data.oem_numbers || [],
            };
        }
        catch (error) {
            logger_1.logger.error(`Error getting Exist.ru part details for ${partId}:`, error);
            return null;
        }
    }
    async searchByOEM(oemNumber) {
        try {
            const response = await this.client.get('/v1/parts/by-oem', {
                params: {
                    oem: oemNumber,
                },
            });
            return response.data.results.map((item) => ({
                id: item.id,
                brand: item.brand,
                partNumber: item.part_number,
                name: item.name,
                price: parseFloat(item.price),
                currency: item.currency || 'RUB',
                availability: this.mapAvailability(item.availability),
                deliveryDays: item.delivery_days || 0,
                description: item.description,
                imageUrl: item.image_url,
                weight: item.weight,
                oem: item.oem_numbers || [],
            }));
        }
        catch (error) {
            logger_1.logger.error(`Error searching by OEM ${oemNumber}:`, error);
            return [];
        }
    }
    async getCarCategories(make, model, year) {
        try {
            const response = await this.client.get('/v1/categories', {
                params: {
                    make,
                    model,
                    year,
                },
            });
            return response.data.categories || [];
        }
        catch (error) {
            logger_1.logger.error(`Error getting categories for ${make} ${model} ${year}:`, error);
            return [];
        }
    }
    async checkHealth() {
        try {
            await this.client.get('/v1/health');
            return true;
        }
        catch (error) {
            logger_1.logger.warn('Exist.ru API health check failed:', error);
            return false;
        }
    }
    mapAvailability(status) {
        if (typeof status === 'string') {
            switch (status.toLowerCase()) {
                case 'in_stock':
                case 'available':
                case 'в наличии':
                    return 'in_stock';
                case 'order':
                case 'под заказ':
                    return 'order';
                default:
                    return 'out_of_stock';
            }
        }
        if (typeof status === 'number') {
            return status > 0 ? 'in_stock' : 'out_of_stock';
        }
        return 'out_of_stock';
    }
}
exports.ExistService = ExistService;
//# sourceMappingURL=exist.service.js.map