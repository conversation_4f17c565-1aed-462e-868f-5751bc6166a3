"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIService = void 0;
const proxyapi_service_1 = require("./proxyapi.service");
const openrouter_service_1 = require("./openrouter.service");
class OpenAIService {
    constructor() {
        this.proxyAPIService = new proxyapi_service_1.ProxyAPIService();
        this.openRouterService = new openrouter_service_1.OpenRouterService();
    }
    async recognizeCar(imageUrl) {
        return this.openRouterService.recognizeCar(imageUrl);
    }
    generateModificationPrompt(carInfo, modifications, userRequest) {
        const carDescription = `${carInfo.year} ${carInfo.make} ${carInfo.model}`;
        const bodyType = carInfo.bodyType.toLowerCase();
        return `Professional automotive photography of a modified ${carDescription} ${bodyType}.

    Base vehicle: ${carDescription} in ${bodyType} configuration
    Applied modifications: ${modifications.join(', ')}
    User specification: "${userRequest}"

    Visual requirements:
    - Ultra-realistic automotive photography quality
    - Professional studio lighting or natural outdoor lighting
    - Clean background (white studio or scenic outdoor setting)
    - 3/4 front view angle showing modifications clearly
    - High-resolution detail showing all modification elements
    - Commercially available aftermarket parts only
    - Professional installation appearance
    - Maintain original vehicle proportions and stance

    Modification details:
    - All parts should look like real, purchasable aftermarket components
    - Proper fitment and finish matching the vehicle
    - Realistic material textures (carbon fiber, painted plastic, metal)
    - Appropriate sizing for the specific car model
    - Professional paint matching where applicable

    Photography style: Automotive magazine quality, sharp focus, proper exposure, vibrant but realistic colors.`;
    }
    async generateModifiedImage(originalImageUrl, carInfo, modifications, userRequest) {
        return this.proxyAPIService.generateModifiedImage(originalImageUrl, carInfo, modifications, userRequest);
    }
    async analyzeModifications(originalImageUrl, modifiedImageUrl, carInfo) {
        return this.openRouterService.analyzeModifications(originalImageUrl, modifiedImageUrl, carInfo);
    }
}
exports.OpenAIService = OpenAIService;
//# sourceMappingURL=openai.service.js.map