"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenRouterService = void 0;
const axios_1 = __importDefault(require("axios"));
const index_1 = require("../config/index");
const logger_1 = require("../utils/logger");
class OpenRouterService {
    constructor() {
        this.client = axios_1.default.create({
            baseURL: index_1.openRouterConfig.baseURL,
            headers: {
                'Authorization': `Bearer ${index_1.openRouterConfig.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://pimpmyride.ai',
                'X-Title': 'PimpMyRideAI',
            },
            timeout: 60000,
        });
    }
    async recognizeCar(imageUrl) {
        try {
            logger_1.logger.info('Starting car recognition with OpenRouter');
            const response = await this.client.post('/chat/completions', {
                model: index_1.openRouterConfig.models.vision,
                messages: [
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: `Analyze this car image and provide detailed information. Return a JSON response with the following structure:
                {
                  "make": "car manufacturer",
                  "model": "car model",
                  "year": estimated_year_number,
                  "generation": "generation if identifiable",
                  "bodyType": "sedan/hatchback/suv/coupe/etc",
                  "confidence": confidence_score_0_to_1,
                  "detectedFeatures": ["list", "of", "visible", "features"],
                  "suggestions": ["modification", "suggestions", "based", "on", "car", "type"]
                }
                
                Be as accurate as possible. If you're not sure about something, indicate lower confidence.`,
                            },
                            {
                                type: 'image_url',
                                image_url: {
                                    url: imageUrl,
                                    detail: 'high',
                                },
                            },
                        ],
                    },
                ],
                max_tokens: 1000,
                temperature: 0.1,
            });
            const content = response.data.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No response from OpenRouter');
            }
            logger_1.logger.info('OpenRouter response content:', content);
            let parsedResponse;
            try {
                let cleanContent = content.replace(/```json\s*|\s*```/g, '').trim();
                const jsonMatch = cleanContent.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    parsedResponse = JSON.parse(jsonMatch[0]);
                }
                else {
                    throw new Error('No JSON found in response');
                }
            }
            catch (jsonError) {
                logger_1.logger.warn('Failed to parse JSON, using fallback parsing:', jsonError);
                parsedResponse = {
                    make: 'Unknown',
                    model: 'Unknown',
                    year: 2020,
                    generation: 'Unknown',
                    bodyType: 'sedan',
                    confidence: 0.5,
                    detectedFeatures: [],
                    suggestions: []
                };
            }
            const carInfo = {
                id: `car_${Date.now()}`,
                make: parsedResponse.make,
                model: parsedResponse.model,
                year: parsedResponse.year,
                generation: parsedResponse.generation,
                bodyType: parsedResponse.bodyType,
                confidence: parsedResponse.confidence,
                detectedFeatures: parsedResponse.detectedFeatures || [],
            };
            logger_1.logger.info(`Car recognized with OpenRouter: ${carInfo.make} ${carInfo.model} ${carInfo.year}`);
            return {
                carInfo,
                suggestions: parsedResponse.suggestions || [],
                confidence: parsedResponse.confidence,
            };
        }
        catch (error) {
            logger_1.logger.error('Error recognizing car with OpenRouter:', {
                message: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined
            });
            if (axios_1.default.isAxiosError(error)) {
                logger_1.logger.error('OpenRouter error details:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                });
            }
            throw new Error('Failed to recognize car from image with OpenRouter');
        }
    }
    async analyzeModifications(originalImageUrl, modifiedImageUrl, carInfo) {
        try {
            logger_1.logger.info('Analyzing applied modifications with OpenRouter');
            const response = await this.client.post('/chat/completions', {
                model: index_1.openRouterConfig.models.vision,
                messages: [
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: `Compare these two images of the same ${carInfo.year} ${carInfo.make} ${carInfo.model}. 
                The second image shows modifications applied to the first image.
                
                Provide a detailed analysis of what modifications were added:
                1. List specific parts that were added or changed
                2. Describe the style and type of each modification
                3. Estimate the visual impact of each change
                4. Mention any brand names or specific part types if identifiable
                
                Format your response as a detailed description that could be used for parts ordering.`,
                            },
                            {
                                type: 'image_url',
                                image_url: { url: originalImageUrl, detail: 'high' },
                            },
                            {
                                type: 'image_url',
                                image_url: { url: modifiedImageUrl, detail: 'high' },
                            },
                        ],
                    },
                ],
                max_tokens: 800,
                temperature: 0.3,
            });
            const analysis = response.data.choices[0]?.message?.content;
            if (!analysis) {
                throw new Error('No analysis generated');
            }
            logger_1.logger.info('Modification analysis completed with OpenRouter');
            return analysis;
        }
        catch (error) {
            logger_1.logger.error('Error analyzing modifications with OpenRouter:', error);
            if (axios_1.default.isAxiosError(error)) {
                logger_1.logger.error('OpenRouter error details:', {
                    status: error.response?.status,
                    data: error.response?.data,
                });
            }
            throw new Error('Failed to analyze modifications with OpenRouter');
        }
    }
    async testConnection() {
        try {
            const response = await this.client.post('/chat/completions', {
                model: index_1.openRouterConfig.models.text,
                messages: [
                    {
                        role: 'user',
                        content: 'Hello, this is a test message. Please respond with "OK".',
                    },
                ],
                max_tokens: 10,
            });
            return response.status === 200 && response.data?.choices?.[0]?.message?.content;
        }
        catch (error) {
            logger_1.logger.error('OpenRouter connection test failed:', error);
            return false;
        }
    }
}
exports.OpenRouterService = OpenRouterService;
//# sourceMappingURL=openrouter.service.js.map