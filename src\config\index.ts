import dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const configSchema = z.object({
  // Telegram Configuration
  TELEGRAM_BOT_TOKEN: z.string().min(1, 'Telegram bot token is required'),

  // ProxyAPI Configuration (for image editing)
  PROXYAPI_KEY: z.string().min(1, 'ProxyAPI key is required'),
  PROXYAPI_IMAGE_MODEL: z.string().default('gpt-image-1'),

  // Image Processing Configuration
  IMAGE_OUTPUT_WIDTH: z.string().transform(Number).default('1920'),
  IMAGE_OUTPUT_HEIGHT: z.string().transform(Number).default('1024'),
  IMAGE_QUALITY: z.enum(['low', 'medium', 'high']).default('high'),

  // OpenRouter Configuration (for image analysis)
  OPENROUTER_API_KEY: z.string().min(1, 'OpenRouter API key is required'),
  OPENROUTER_MODEL_VISION: z.string().default('openai/gpt-4o'),
  OPENROUTER_MODEL_TEXT: z.string().default('openai/gpt-4o-mini'),
  
  // Database Configuration
  DATABASE_URL: z.string().url('Valid database URL is required'),
  
  // Server Configuration
  PORT: z.string().transform(Number).default('3000'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // File Configuration
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  
  // Logging Configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Cache Configuration
  REDIS_URL: z.string().optional(),
  CACHE_TTL: z.string().transform(Number).default('3600'), // 1 hour
  
  // External APIs (optional)
  ABCP_API_KEY: z.string().optional(),
  EXIST_API_KEY: z.string().optional(),
  AUTODOC_API_KEY: z.string().optional(),
});

type Config = z.infer<typeof configSchema>;

function validateConfig(): Config {
  try {
    return configSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingFields = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Configuration validation failed:\n${missingFields.join('\n')}`);
    }
    throw error;
  }
}

export const config = validateConfig();

export const proxyAPIConfig = {
  apiKey: config.PROXYAPI_KEY,
  baseURL: 'https://api.proxyapi.ru/openai/v1',
  models: {
    image: config.PROXYAPI_IMAGE_MODEL,
  },
} as const;

export const imageConfig = {
  outputWidth: config.IMAGE_OUTPUT_WIDTH,
  outputHeight: config.IMAGE_OUTPUT_HEIGHT,
  quality: config.IMAGE_QUALITY,
  aspectRatio: config.IMAGE_OUTPUT_WIDTH / config.IMAGE_OUTPUT_HEIGHT,
  supportedSizes: [
    '1920x1024', // Primary target resolution
    '1536x1024', // Fallback horizontal
    '1024x1536', // Fallback vertical
    '1024x1024', // Standard fallback
  ],
} as const;

export const openRouterConfig = {
  apiKey: config.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
  models: {
    vision: config.OPENROUTER_MODEL_VISION,
    text: config.OPENROUTER_MODEL_TEXT,
  },
} as const;

export const telegramConfig = {
  token: config.TELEGRAM_BOT_TOKEN,
  options: {
    polling: true,
    filepath: false,
  },
} as const;

export const databaseConfig = {
  url: config.DATABASE_URL,
} as const;

export const serverConfig = {
  port: config.PORT,
  env: config.NODE_ENV,
  uploadDir: config.UPLOAD_DIR,
  maxFileSize: config.MAX_FILE_SIZE,
} as const;

export const loggingConfig = {
  level: config.LOG_LEVEL,
  file: config.LOG_FILE,
} as const;

export const rateLimitConfig = {
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  maxRequests: config.RATE_LIMIT_MAX_REQUESTS,
} as const;

export const cacheConfig = {
  redisUrl: config.REDIS_URL,
  ttl: config.CACHE_TTL,
} as const;

export const externalApiConfig = {
  abcp: {
    apiKey: config.ABCP_API_KEY,
    baseURL: 'https://api.abcp.ru',
  },
  exist: {
    apiKey: config.EXIST_API_KEY,
    baseURL: 'https://api.exist.ru', // Hypothetical endpoint
  },
  autodoc: {
    apiKey: config.AUTODOC_API_KEY,
    baseURL: 'https://api.autodoc.com',
  },
} as const;
