import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

async function seedDatabase() {
  try {
    logger.info('🌱 Starting database seeding...');

    // 1. Seed modification categories
    const categories = [
      {
        name: 'body_kit',
        description: 'Аэродинамические обвесы, бамперы, пороги, спойлеры'
      },
      {
        name: 'wheels',
        description: 'Диски, шины, проставки'
      },
      {
        name: 'suspension',
        description: 'Подвеска, амортизаторы, пружины, койловеры'
      },
      {
        name: 'exhaust',
        description: 'Выхлопная система, глушители, катализаторы'
      },
      {
        name: 'lighting',
        description: 'Фары, задние фонари, LED подсветка'
      },
      {
        name: 'interior',
        description: 'Салон, сиденья, руль, панели'
      },
      {
        name: 'performance',
        description: 'Чип-тюнинг, турбины, интеркулеры'
      },
      {
        name: 'exterior',
        description: 'Внешний тюнинг, винил, пленки'
      }
    ];

    for (const category of categories) {
      await prisma.modificationCategory.upsert({
        where: { name: category.name },
        update: {},
        create: category
      });
    }

    logger.info('✅ Categories seeded');

    // 2. Seed popular cars
    const cars = [
      { make: 'BMW', model: '3 Series', year: 2020, generation: 'G20', bodyType: 'Sedan' },
      { make: 'BMW', model: '5 Series', year: 2019, generation: 'G30', bodyType: 'Sedan' },
      { make: 'BMW', model: 'X5', year: 2021, generation: 'G05', bodyType: 'SUV' },
      { make: 'Mercedes-Benz', model: 'C-Class', year: 2020, generation: 'W205', bodyType: 'Sedan' },
      { make: 'Mercedes-Benz', model: 'E-Class', year: 2019, generation: 'W213', bodyType: 'Sedan' },
      { make: 'Mercedes-Benz', model: 'GLE', year: 2021, generation: 'W167', bodyType: 'SUV' },
      { make: 'Audi', model: 'A4', year: 2020, generation: 'B9', bodyType: 'Sedan' },
      { make: 'Audi', model: 'A6', year: 2019, generation: 'C8', bodyType: 'Sedan' },
      { make: 'Audi', model: 'Q7', year: 2021, generation: '4M', bodyType: 'SUV' },
      { make: 'Toyota', model: 'Camry', year: 2020, generation: 'XV70', bodyType: 'Sedan' },
      { make: 'Toyota', model: 'RAV4', year: 2021, generation: 'XA50', bodyType: 'SUV' },
      { make: 'Honda', model: 'Civic', year: 2020, generation: 'FC', bodyType: 'Sedan' },
      { make: 'Honda', model: 'CR-V', year: 2021, generation: 'RW', bodyType: 'SUV' },
      { make: 'Volkswagen', model: 'Golf', year: 2020, generation: 'Mk8', bodyType: 'Hatchback' },
      { make: 'Volkswagen', model: 'Passat', year: 2019, generation: 'B8', bodyType: 'Sedan' },
      { make: 'Ford', model: 'Focus', year: 2020, generation: 'C519', bodyType: 'Hatchback' },
      { make: 'Ford', model: 'Mustang', year: 2021, generation: 'S550', bodyType: 'Coupe' },
      { make: 'Hyundai', model: 'Elantra', year: 2020, generation: 'CN7', bodyType: 'Sedan' },
      { make: 'Hyundai', model: 'Tucson', year: 2021, generation: 'NX4', bodyType: 'SUV' },
      { make: 'Kia', model: 'Optima', year: 2020, generation: 'JF', bodyType: 'Sedan' }
    ];

    for (const car of cars) {
      await prisma.car.upsert({
        where: {
          make_model_year_generation: {
            make: car.make,
            model: car.model,
            year: car.year,
            generation: car.generation
          }
        },
        update: {},
        create: car
      });
    }

    logger.info('✅ Cars seeded');

    // 3. Seed modification parts
    const bodyKitCategory = await prisma.modificationCategory.findUnique({
      where: { name: 'body_kit' }
    });

    const wheelsCategory = await prisma.modificationCategory.findUnique({
      where: { name: 'wheels' }
    });

    const suspensionCategory = await prisma.modificationCategory.findUnique({
      where: { name: 'suspension' }
    });

    if (bodyKitCategory && wheelsCategory && suspensionCategory) {
      const parts = [
        // Body Kit Parts
        {
          name: 'Спортивный передний бампер',
          categoryId: bodyKitCategory.id,
          description: 'Агрессивный передний бампер с воздухозаборниками',
          averagePrice: 25000,
          installationComplexity: 'medium',
          brand: 'Universal'
        },
        {
          name: 'Боковые пороги',
          categoryId: bodyKitCategory.id,
          description: 'Спортивные боковые пороги для улучшения аэродинамики',
          averagePrice: 15000,
          installationComplexity: 'medium',
          brand: 'Universal'
        },
        {
          name: 'Задний диффузор',
          categoryId: bodyKitCategory.id,
          description: 'Карбоновый задний диффузор',
          averagePrice: 18000,
          installationComplexity: 'easy',
          brand: 'Universal'
        },
        {
          name: 'Спойлер на крышку багажника',
          categoryId: bodyKitCategory.id,
          description: 'Спортивный спойлер для улучшения прижимной силы',
          averagePrice: 12000,
          installationComplexity: 'easy',
          brand: 'Universal'
        },
        // Wheels
        {
          name: 'Легкосплавные диски 19"',
          categoryId: wheelsCategory.id,
          description: 'Кованые диски 19 дюймов, черный матовый',
          averagePrice: 80000,
          installationComplexity: 'easy',
          brand: 'BBS'
        },
        {
          name: 'Легкосплавные диски 20"',
          categoryId: wheelsCategory.id,
          description: 'Кованые диски 20 дюймов, хром',
          averagePrice: 120000,
          installationComplexity: 'easy',
          brand: 'HRE'
        },
        {
          name: 'Спортивные шины',
          categoryId: wheelsCategory.id,
          description: 'Высокопроизводительные летние шины',
          averagePrice: 40000,
          installationComplexity: 'easy',
          brand: 'Michelin'
        },
        // Suspension
        {
          name: 'Койловеры',
          categoryId: suspensionCategory.id,
          description: 'Регулируемая спортивная подвеска',
          averagePrice: 150000,
          installationComplexity: 'hard',
          brand: 'KW'
        },
        {
          name: 'Спортивные пружины',
          categoryId: suspensionCategory.id,
          description: 'Укороченные спортивные пружины -30мм',
          averagePrice: 25000,
          installationComplexity: 'medium',
          brand: 'Eibach'
        },
        {
          name: 'Стабилизаторы поперечной устойчивости',
          categoryId: suspensionCategory.id,
          description: 'Усиленные стабилизаторы для лучшей управляемости',
          averagePrice: 35000,
          installationComplexity: 'medium',
          brand: 'H&R'
        }
      ];

      for (const part of parts) {
        await prisma.modificationPart.create({
          data: part
        });
      }

      logger.info('✅ Modification parts seeded');
    }

    logger.info('🎉 Database seeding completed successfully!');

  } catch (error) {
    logger.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedDatabase()
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
