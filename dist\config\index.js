"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.externalApiConfig = exports.cacheConfig = exports.rateLimitConfig = exports.loggingConfig = exports.serverConfig = exports.databaseConfig = exports.telegramConfig = exports.openRouterConfig = exports.imageConfig = exports.proxyAPIConfig = exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
dotenv_1.default.config();
const configSchema = zod_1.z.object({
    TELEGRAM_BOT_TOKEN: zod_1.z.string().min(1, 'Telegram bot token is required'),
    PROXYAPI_KEY: zod_1.z.string().min(1, 'ProxyAPI key is required'),
    PROXYAPI_IMAGE_MODEL: zod_1.z.string().default('gpt-image-1'),
    IMAGE_OUTPUT_WIDTH: zod_1.z.string().transform(Number).default('1920'),
    IMAGE_OUTPUT_HEIGHT: zod_1.z.string().transform(Number).default('1024'),
    IMAGE_QUALITY: zod_1.z.enum(['low', 'medium', 'high']).default('high'),
    OPENROUTER_API_KEY: zod_1.z.string().min(1, 'OpenRouter API key is required'),
    OPENROUTER_MODEL_VISION: zod_1.z.string().default('openai/gpt-4o'),
    OPENROUTER_MODEL_TEXT: zod_1.z.string().default('openai/gpt-4o-mini'),
    DATABASE_URL: zod_1.z.string().url('Valid database URL is required'),
    PORT: zod_1.z.string().transform(Number).default('3000'),
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    UPLOAD_DIR: zod_1.z.string().default('./uploads'),
    MAX_FILE_SIZE: zod_1.z.string().transform(Number).default('10485760'),
    LOG_LEVEL: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    LOG_FILE: zod_1.z.string().default('./logs/app.log'),
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().transform(Number).default('900000'),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().transform(Number).default('100'),
    REDIS_URL: zod_1.z.string().optional(),
    CACHE_TTL: zod_1.z.string().transform(Number).default('3600'),
    ABCP_API_KEY: zod_1.z.string().optional(),
    EXIST_API_KEY: zod_1.z.string().optional(),
    AUTODOC_API_KEY: zod_1.z.string().optional(),
});
function validateConfig() {
    try {
        return configSchema.parse(process.env);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            const missingFields = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Configuration validation failed:\n${missingFields.join('\n')}`);
        }
        throw error;
    }
}
exports.config = validateConfig();
exports.proxyAPIConfig = {
    apiKey: exports.config.PROXYAPI_KEY,
    baseURL: 'https://api.proxyapi.ru/openai/v1',
    models: {
        image: exports.config.PROXYAPI_IMAGE_MODEL,
    },
};
exports.imageConfig = {
    outputWidth: exports.config.IMAGE_OUTPUT_WIDTH,
    outputHeight: exports.config.IMAGE_OUTPUT_HEIGHT,
    quality: exports.config.IMAGE_QUALITY,
    aspectRatio: exports.config.IMAGE_OUTPUT_WIDTH / exports.config.IMAGE_OUTPUT_HEIGHT,
    supportedSizes: [
        '1920x1024',
        '1536x1024',
        '1024x1536',
        '1024x1024',
    ],
};
exports.openRouterConfig = {
    apiKey: exports.config.OPENROUTER_API_KEY,
    baseURL: 'https://openrouter.ai/api/v1',
    models: {
        vision: exports.config.OPENROUTER_MODEL_VISION,
        text: exports.config.OPENROUTER_MODEL_TEXT,
    },
};
exports.telegramConfig = {
    token: exports.config.TELEGRAM_BOT_TOKEN,
    options: {
        polling: true,
        filepath: false,
    },
};
exports.databaseConfig = {
    url: exports.config.DATABASE_URL,
};
exports.serverConfig = {
    port: exports.config.PORT,
    env: exports.config.NODE_ENV,
    uploadDir: exports.config.UPLOAD_DIR,
    maxFileSize: exports.config.MAX_FILE_SIZE,
};
exports.loggingConfig = {
    level: exports.config.LOG_LEVEL,
    file: exports.config.LOG_FILE,
};
exports.rateLimitConfig = {
    windowMs: exports.config.RATE_LIMIT_WINDOW_MS,
    maxRequests: exports.config.RATE_LIMIT_MAX_REQUESTS,
};
exports.cacheConfig = {
    redisUrl: exports.config.REDIS_URL,
    ttl: exports.config.CACHE_TTL,
};
exports.externalApiConfig = {
    abcp: {
        apiKey: exports.config.ABCP_API_KEY,
        baseURL: 'https://api.abcp.ru',
    },
    exist: {
        apiKey: exports.config.EXIST_API_KEY,
        baseURL: 'https://api.exist.ru',
    },
    autodoc: {
        apiKey: exports.config.AUTODOC_API_KEY,
        baseURL: 'https://api.autodoc.com',
    },
};
//# sourceMappingURL=index.js.map