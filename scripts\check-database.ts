import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

interface DatabaseStats {
  users: number;
  sessions: number;
  cars: number;
  categories: number;
  parts: number;
  modifications: number;
  apiUsage: number;
  priceHistory: number;
}

async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ Database connection successful');
    return true;
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    return false;
  }
}

async function getDatabaseStats(): Promise<DatabaseStats> {
  try {
    const [
      users,
      sessions,
      cars,
      categories,
      parts,
      modifications,
      apiUsage,
      priceHistory
    ] = await Promise.all([
      prisma.user.count(),
      prisma.userSession.count(),
      prisma.car.count(),
      prisma.modificationCategory.count(),
      prisma.modificationPart.count(),
      prisma.modificationHistory.count(),
      prisma.apiUsage.count(),
      prisma.priceHistory.count()
    ]);

    return {
      users,
      sessions,
      cars,
      categories,
      parts,
      modifications,
      apiUsage,
      priceHistory
    };
  } catch (error) {
    logger.error('Error getting database stats:', error);
    throw error;
  }
}

async function checkTableStructure(): Promise<void> {
  try {
    logger.info('🔍 Checking table structure...');

    // Check if all required tables exist
    const tables = [
      'users',
      'user_sessions',
      'cars',
      'modification_categories',
      'modification_parts',
      'modification_history',
      'price_history',
      'api_usage'
    ];

    for (const table of tables) {
      try {
        const result = await prisma.$queryRawUnsafe(`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_name = '${table}' AND table_schema = 'public'
        `);
        
        const count = (result as any)[0]?.count;
        if (parseInt(count) > 0) {
          logger.info(`✅ Table '${table}' exists`);
        } else {
          logger.error(`❌ Table '${table}' does not exist`);
        }
      } catch (error) {
        logger.error(`❌ Error checking table '${table}':`, error);
      }
    }
  } catch (error) {
    logger.error('Error checking table structure:', error);
    throw error;
  }
}

async function checkIndexes(): Promise<void> {
  try {
    logger.info('🔍 Checking database indexes...');

    const indexes = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname
    `;

    logger.info(`Found ${(indexes as any[]).length} indexes`);
    
    // Check for important indexes
    const importantIndexes = [
      'users_telegram_id_key',
      'modification_categories_name_key',
      'cars_make_model_year_generation_key'
    ];

    for (const indexName of importantIndexes) {
      const found = (indexes as any[]).some(idx => idx.indexname === indexName);
      if (found) {
        logger.info(`✅ Important index '${indexName}' exists`);
      } else {
        logger.warn(`⚠️ Important index '${indexName}' missing`);
      }
    }
  } catch (error) {
    logger.error('Error checking indexes:', error);
    throw error;
  }
}

async function checkConstraints(): Promise<void> {
  try {
    logger.info('🔍 Checking foreign key constraints...');

    const constraints = await prisma.$queryRaw`
      SELECT 
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_name
    `;

    logger.info(`Found ${(constraints as any[]).length} foreign key constraints`);
  } catch (error) {
    logger.error('Error checking constraints:', error);
    throw error;
  }
}

async function performHealthCheck(): Promise<void> {
  try {
    logger.info('🏥 Starting database health check...');

    // 1. Check connection
    const connected = await checkDatabaseConnection();
    if (!connected) {
      throw new Error('Database connection failed');
    }

    // 2. Check table structure
    await checkTableStructure();

    // 3. Get statistics
    const stats = await getDatabaseStats();
    
    logger.info('📊 Database Statistics:');
    logger.info(`   Users: ${stats.users}`);
    logger.info(`   Active Sessions: ${stats.sessions}`);
    logger.info(`   Cars: ${stats.cars}`);
    logger.info(`   Modification Categories: ${stats.categories}`);
    logger.info(`   Modification Parts: ${stats.parts}`);
    logger.info(`   Modification History: ${stats.modifications}`);
    logger.info(`   API Usage Records: ${stats.apiUsage}`);
    logger.info(`   Price History Records: ${stats.priceHistory}`);

    // 4. Check indexes
    await checkIndexes();

    // 5. Check constraints
    await checkConstraints();

    // 6. Check if database is properly seeded
    if (stats.categories === 0) {
      logger.warn('⚠️ No modification categories found. Run seed script: npm run db:seed');
    }

    if (stats.cars === 0) {
      logger.warn('⚠️ No cars found. Run seed script: npm run db:seed');
    }

    if (stats.parts === 0) {
      logger.warn('⚠️ No modification parts found. Run seed script: npm run db:seed');
    }

    logger.info('✅ Database health check completed successfully!');

  } catch (error) {
    logger.error('❌ Database health check failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run health check if called directly
if (require.main === module) {
  performHealthCheck()
    .catch((error) => {
      console.error('Health check failed:', error);
      process.exit(1);
    });
}

export { performHealthCheck, getDatabaseStats, checkDatabaseConnection };
