import { ProxyAPIService } from '@/services/proxyapi.service';
import { OpenRouterService } from '@/services/openrouter.service';
import { logger } from '@/utils/logger';

/**
 * Utility for testing API connections and functionality
 */
export class APITestUtils {
  private proxyAPIService: ProxyAPIService;
  private openRouterService: OpenRouterService;

  constructor() {
    this.proxyAPIService = new ProxyAPIService();
    this.openRouterService = new OpenRouterService();
  }

  /**
   * Test all API connections
   */
  async testAllConnections(): Promise<{
    proxyAPI: boolean;
    openRouter: boolean;
    overall: boolean;
  }> {
    logger.info('Testing API connections...');

    const proxyAPIResult = await this.testProxyAPI();
    const openRouterResult = await this.testOpenRouter();

    const overall = proxyAPIResult && openRouterResult;

    logger.info('API connection test results:', {
      proxyAPI: proxyAPIResult,
      openRouter: openRouterResult,
      overall,
    });

    return {
      proxyAPI: proxyAPIResult,
      openRouter: openRouterResult,
      overall,
    };
  }

  /**
   * Test ProxyAPI connection
   */
  async testProxyAPI(): Promise<boolean> {
    try {
      logger.info('Testing ProxyAPI connection...');
      const result = await this.proxyAPIService.testConnection();
      logger.info(`ProxyAPI test result: ${result ? 'SUCCESS' : 'FAILED'}`);
      return result;
    } catch (error) {
      logger.error('ProxyAPI test failed:', error);
      return false;
    }
  }

  /**
   * Test OpenRouter connection
   */
  async testOpenRouter(): Promise<boolean> {
    try {
      logger.info('Testing OpenRouter connection...');
      const result = await this.openRouterService.testConnection();
      logger.info(`OpenRouter test result: ${result ? 'SUCCESS' : 'FAILED'}`);
      return result;
    } catch (error) {
      logger.error('OpenRouter test failed:', error);
      return false;
    }
  }

  /**
   * Test car recognition with a sample image
   */
  async testCarRecognition(imageUrl: string): Promise<boolean> {
    try {
      logger.info('Testing car recognition...');
      const result = await this.openRouterService.recognizeCar(imageUrl);
      
      if (result && result.carInfo && result.carInfo.make && result.carInfo.model) {
        logger.info('Car recognition test SUCCESS:', {
          make: result.carInfo.make,
          model: result.carInfo.model,
          year: result.carInfo.year,
          confidence: result.confidence,
        });
        return true;
      } else {
        logger.warn('Car recognition test FAILED: Invalid response format');
        return false;
      }
    } catch (error) {
      logger.error('Car recognition test failed:', error);
      return false;
    }
  }

  /**
   * Test image generation with a simple prompt
   */
  async testImageGeneration(): Promise<boolean> {
    try {
      logger.info('Testing image generation...');
      
      // Create a simple test car info
      const testCarInfo = {
        id: 'test_car',
        make: 'BMW',
        model: '3 Series',
        year: 2020,
        bodyType: 'sedan',
        confidence: 0.9,
        detectedFeatures: ['headlights', 'grille', 'wheels'],
      };

      const result = await this.proxyAPIService.generateModifiedImage(
        'https://example.com/test-car.jpg', // This won't be used for gpt-image-1
        testCarInfo,
        ['sport body kit', 'black wheels'],
        'Make it look sporty'
      );

      if (result && result.url) {
        logger.info('Image generation test SUCCESS');
        return true;
      } else {
        logger.warn('Image generation test FAILED: No image URL returned');
        return false;
      }
    } catch (error) {
      logger.error('Image generation test failed:', error);
      return false;
    }
  }

  /**
   * Run comprehensive API tests
   */
  async runComprehensiveTests(testImageUrl?: string): Promise<{
    connections: { proxyAPI: boolean; openRouter: boolean; overall: boolean };
    carRecognition?: boolean;
    imageGeneration?: boolean;
    overallSuccess: boolean;
  }> {
    logger.info('Running comprehensive API tests...');

    const connections = await this.testAllConnections();
    
    const results: any = { connections };

    // Only run advanced tests if basic connections work
    if (connections.overall) {
      if (testImageUrl) {
        results.carRecognition = await this.testCarRecognition(testImageUrl);
      }
      
      results.imageGeneration = await this.testImageGeneration();
    }

    results.overallSuccess = connections.overall && 
      (results.carRecognition !== false) && 
      (results.imageGeneration !== false);

    logger.info('Comprehensive test results:', results);
    return results;
  }
}

// Export a singleton instance for easy use
export const apiTestUtils = new APITestUtils();
