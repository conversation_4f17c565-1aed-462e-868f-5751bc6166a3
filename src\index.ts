// Initialize module aliases for runtime path resolution
import 'module-alias/register';

import { config } from '@/config';
import { TelegramService } from '@/services/telegram.service';
import { logger } from '@/utils/logger';

/**
 * Main application entry point
 */
class CarModificationBot {
  private telegramService: TelegramService;

  constructor() {
    this.telegramService = new TelegramService();
  }

  /**
   * Start the bot
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting Car Modification Generator Bot...');
      logger.info(`Environment: ${config.NODE_ENV}`);
      logger.info(`Log level: ${config.LOG_LEVEL}`);

      // Start Telegram service
      this.telegramService.start();

      logger.info('🚗 Car Modification Generator Bot started successfully!');
      logger.info('Bot is ready to receive messages...');

    } catch (error) {
      logger.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down Car Modification Generator Bot...');
      
      this.telegramService.stop();
      
      logger.info('Bot shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Create bot instance
const bot = new CarModificationBot();

// Handle process signals for graceful shutdown
process.on('SIGINT', () => {
  logger.info('Received SIGINT signal');
  bot.shutdown();
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM signal');
  bot.shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  bot.shutdown();
});

// Start the bot
bot.start().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
