import axios, { AxiosInstance } from 'axios';
import { config } from '@/config';
import { logger } from '@/utils/logger';

export interface AbcpPartInfo {
  id: string;
  brand: string;
  number: string;
  name: string;
  price: number;
  currency: string;
  availability: number;
  deliveryDays: number;
  supplier: string;
  weight?: number;
  imageUrl?: string;
}

export interface AbcpSearchParams {
  query: string;
  brand?: string;
  limit?: number;
  offset?: number;
}

export interface AbcpSearchResponse {
  parts: AbcpPartInfo[];
  total: number;
  hasMore: boolean;
}

/**
 * Service for integration with ABCP.ru API
 * Documentation: https://www.abcp.ru/wiki/API
 */
export class AbcpService {
  private client: AxiosInstance;
  private readonly baseURL = 'https://api.abcp.ru';

  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PimpMyRideAI/1.0',
      },
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      if (process.env.ABCP_API_KEY) {
        config.headers['Authorization'] = `Bearer ${process.env.ABCP_API_KEY}`;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        logger.error('ABCP API Error:', {
          status: error.response?.status,
          message: error.response?.data?.message || error.message,
          url: error.config?.url,
        });
        throw error;
      }
    );
  }

  /**
   * Search for parts by query
   */
  async searchParts(params: AbcpSearchParams): Promise<AbcpSearchResponse> {
    try {
      logger.info(`Searching ABCP for: ${params.query}`);

      const response = await this.client.get('/search/parts', {
        params: {
          q: params.query,
          brand: params.brand,
          limit: params.limit || 20,
          offset: params.offset || 0,
        },
      });

      const parts: AbcpPartInfo[] = response.data.items.map((item: any) => ({
        id: item.id,
        brand: item.brand,
        number: item.number,
        name: item.name,
        price: parseFloat(item.price),
        currency: item.currency || 'RUB',
        availability: item.availability || 0,
        deliveryDays: item.deliveryDays || 0,
        supplier: item.supplier,
        weight: item.weight,
        imageUrl: item.imageUrl,
      }));

      return {
        parts,
        total: response.data.total,
        hasMore: response.data.hasMore,
      };
    } catch (error) {
      logger.error('Error searching ABCP parts:', error);
      throw new Error('Failed to search parts in ABCP');
    }
  }

  /**
   * Get part details by ID
   */
  async getPartDetails(partId: string): Promise<AbcpPartInfo | null> {
    try {
      const response = await this.client.get(`/parts/${partId}`);
      
      return {
        id: response.data.id,
        brand: response.data.brand,
        number: response.data.number,
        name: response.data.name,
        price: parseFloat(response.data.price),
        currency: response.data.currency || 'RUB',
        availability: response.data.availability || 0,
        deliveryDays: response.data.deliveryDays || 0,
        supplier: response.data.supplier,
        weight: response.data.weight,
        imageUrl: response.data.imageUrl,
      };
    } catch (error) {
      logger.error(`Error getting ABCP part details for ${partId}:`, error);
      return null;
    }
  }

  /**
   * Find analogs/crosses for a part
   */
  async findAnalogs(brand: string, partNumber: string): Promise<AbcpPartInfo[]> {
    try {
      const response = await this.client.get('/parts/analogs', {
        params: {
          brand,
          number: partNumber,
        },
      });

      return response.data.items.map((item: any) => ({
        id: item.id,
        brand: item.brand,
        number: item.number,
        name: item.name,
        price: parseFloat(item.price),
        currency: item.currency || 'RUB',
        availability: item.availability || 0,
        deliveryDays: item.deliveryDays || 0,
        supplier: item.supplier,
        weight: item.weight,
        imageUrl: item.imageUrl,
      }));
    } catch (error) {
      logger.error(`Error finding analogs for ${brand} ${partNumber}:`, error);
      return [];
    }
  }

  /**
   * Check if API is available
   */
  async checkHealth(): Promise<boolean> {
    try {
      await this.client.get('/health');
      return true;
    } catch (error) {
      logger.warn('ABCP API health check failed:', error);
      return false;
    }
  }
}
