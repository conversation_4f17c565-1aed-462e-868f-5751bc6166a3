import { z } from 'zod';
import { ModificationCategory, InstallationComplexity } from '@/types';

// Car information validation schema
export const CarInfoSchema = z.object({
  id: z.string(),
  make: z.string().min(1, 'Car make is required'),
  model: z.string().min(1, 'Car model is required'),
  year: z.number().int().min(1900).max(new Date().getFullYear() + 2),
  generation: z.string().optional(),
  bodyType: z.string().min(1, 'Body type is required'),
  confidence: z.number().min(0).max(1),
  detectedFeatures: z.array(z.string()),
});

// Modification request validation schema
export const ModificationRequestSchema = z.object({
  carInfo: CarInfoSchema,
  requestedModifications: z.array(z.string().min(1)),
  userPreferences: z.object({
    budget: z.number().positive().optional(),
    style: z.enum(['sport', 'luxury', 'aggressive', 'elegant']).optional(),
    priority: z.enum(['visual', 'performance', 'balanced']).optional(),
  }).optional(),
});

// User input validation
export const UserInputSchema = z.object({
  text: z.string().min(1).max(1000, 'Message too long'),
  userId: z.number().int().positive(),
  chatId: z.number().int(),
});

// File validation schema
export const FileValidationSchema = z.object({
  fileId: z.string().min(1),
  fileSize: z.number().int().positive().max(10 * 1024 * 1024), // 10MB max
  mimeType: z.string().regex(/^image\/(jpeg|jpg|png|webp)$/),
});

// Pricing validation schema
export const PricingSchema = z.object({
  specificPart: z.string().min(1),
  brand: z.string().min(1),
  partNumber: z.string().optional(),
  price: z.number().positive(),
  averagePrice: z.number().positive(),
  installationTime: z.string().min(1),
  description: z.string().min(1),
});

// Modification category validation
export const ModificationCategorySchema = z.nativeEnum(ModificationCategory);

// Installation complexity validation
export const InstallationComplexitySchema = z.nativeEnum(InstallationComplexity);

/**
 * Validation utilities
 */
export class ValidationUtils {
  /**
   * Validate car information
   */
  static validateCarInfo(data: unknown) {
    return CarInfoSchema.parse(data);
  }

  /**
   * Validate modification request
   */
  static validateModificationRequest(data: unknown) {
    return ModificationRequestSchema.parse(data);
  }

  /**
   * Validate user input
   */
  static validateUserInput(data: unknown) {
    return UserInputSchema.parse(data);
  }

  /**
   * Validate file upload
   */
  static validateFile(data: unknown) {
    return FileValidationSchema.parse(data);
  }

  /**
   * Validate pricing data
   */
  static validatePricing(data: unknown) {
    return PricingSchema.parse(data);
  }

  /**
   * Sanitize user text input
   */
  static sanitizeText(text: string): string {
    return text
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate image URL
   */
  static isValidImageUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  /**
   * Validate modification text
   */
  static validateModificationText(text: string): boolean {
    const sanitized = this.sanitizeText(text);
    
    // Check minimum length
    if (sanitized.length < 3) {
      return false;
    }

    // Check for common modification keywords
    const keywords = [
      'body kit', 'bodykit', 'wheels', 'rims', 'spoiler', 'wing',
      'suspension', 'lower', 'exhaust', 'splitter', 'skirts',
      'aggressive', 'sport', 'racing', 'performance', 'tuning'
    ];

    const lowerText = sanitized.toLowerCase();
    return keywords.some(keyword => lowerText.includes(keyword));
  }

  /**
   * Extract modification keywords from text
   */
  static extractModificationKeywords(text: string): string[] {
    const keywords = [
      'body kit', 'bodykit', 'front splitter', 'side skirts', 'rear diffuser',
      'wheels', 'rims', 'alloys', 'black wheels', 'chrome wheels',
      'spoiler', 'rear spoiler', 'wing', 'roof spoiler',
      'suspension', 'lowered', 'coilovers', 'springs',
      'exhaust', 'cat-back', 'muffler', 'pipes',
      'aggressive', 'sport', 'racing', 'performance'
    ];

    const lowerText = text.toLowerCase();
    return keywords.filter(keyword => lowerText.includes(keyword));
  }

  /**
   * Validate price range
   */
  static validatePriceRange(min: number, max: number): boolean {
    return min >= 0 && max >= min && max <= 50000; // Reasonable price limits
  }

  /**
   * Validate year range
   */
  static validateCarYear(year: number): boolean {
    const currentYear = new Date().getFullYear();
    return year >= 1990 && year <= currentYear + 2;
  }

  /**
   * Check if text contains inappropriate content
   */
  static containsInappropriateContent(text: string): boolean {
    const inappropriateWords = [
      // Add inappropriate words that should be filtered
      'spam', 'scam', 'hack'
    ];

    const lowerText = text.toLowerCase();
    return inappropriateWords.some(word => lowerText.includes(word));
  }

  /**
   * Validate session data
   */
  static validateSessionData(data: any): boolean {
    try {
      return (
        typeof data.userId === 'number' &&
        typeof data.chatId === 'number' &&
        typeof data.currentStep === 'string' &&
        data.lastActivity instanceof Date
      );
    } catch {
      return false;
    }
  }

  /**
   * Rate limiting validation
   */
  static validateRateLimit(
    requestCount: number,
    timeWindow: number,
    maxRequests: number
  ): boolean {
    return requestCount <= maxRequests;
  }
}
