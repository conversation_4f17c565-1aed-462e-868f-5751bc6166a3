# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# ProxyAPI Configuration (for image editing)
PROXYAPI_KEY=your_proxyapi_key_here
PROXYAPI_IMAGE_MODEL=gpt-image-1

# Image Processing Configuration
IMAGE_OUTPUT_WIDTH=1920
IMAGE_OUTPUT_HEIGHT=1024
IMAGE_QUALITY=high

# OpenRouter Configuration (for image analysis)
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL_VISION=openai/gpt-4o
OPENROUTER_MODEL_TEXT=openai/gpt-4o-mini

# Database Configuration
DATABASE_URL=postgresql://pimpmyride:secure_password_123@localhost:5432/car_modifications

# Server Configuration
PORT=3000
NODE_ENV=development

# File Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration (optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# External Auto Parts APIs (optional)
# ABCP.ru - Russian auto parts platform
ABCP_API_KEY=your_abcp_api_key_here

# Exist.ru - Russian auto parts store
EXIST_API_KEY=your_exist_api_key_here

# AutoDoc - European auto parts
AUTODOC_API_KEY=your_autodoc_api_key_here
