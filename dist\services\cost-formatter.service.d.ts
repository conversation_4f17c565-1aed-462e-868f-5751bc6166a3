import { AppliedModification, CostBreakdown } from '@/types';
export declare class CostFormatterService {
    formatCostBreakdown(appliedModifications: AppliedModification[], totalCost: CostBreakdown): string;
    private formatSingleModification;
    private formatCostSummary;
    private formatNotes;
    private convertToDetailedPartInfo;
    private convertToRubles;
    private formatPrice;
    private getAvailabilityIcon;
    private getRandomAvailability;
    private getRandomDeliveryDays;
    private getWarrantyPeriod;
    private getErrorMessage;
    formatSimpleCostSummary(totalCost: CostBreakdown): string;
}
//# sourceMappingURL=cost-formatter.service.d.ts.map