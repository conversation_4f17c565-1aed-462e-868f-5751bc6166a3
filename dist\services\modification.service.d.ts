import { CarInfo, ModificationResult, CarRecognitionResponse } from '@/types';
export declare class ModificationService {
    private openAIService;
    private carRecognitionService;
    private pricingService;
    constructor();
    recognizeCar(imageUrl: string): Promise<CarRecognitionResponse>;
    generateModifications(originalImageUrl: string, carInfo: CarInfo, userRequest: string): Promise<ModificationResult>;
    private generateModificationDescription;
    private parseModificationRequest;
    private generateAppliedModificationsList;
    private createModificationData;
    private formatModificationName;
    private categorizeModification;
    private getInstallationComplexity;
    private calculateTotalCost;
    private generateInstallationNotes;
}
//# sourceMappingURL=modification.service.d.ts.map