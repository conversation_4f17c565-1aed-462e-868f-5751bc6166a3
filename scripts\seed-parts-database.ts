import { PrismaClient } from '@prisma/client';
import { logger } from '../src/utils/logger';

const prisma = new PrismaClient();

// Данные для российского рынка автозапчастей
const categoriesData = [
  { name: 'body_kit', description: 'Обвесы и аэродинамические элементы' },
  { name: 'wheels', description: 'Диски и колеса' },
  { name: 'spoiler', description: 'Спойлеры и антикрылья' },
  { name: 'exhaust', description: 'Выхлопные системы' },
  { name: 'suspension', description: 'Подвеска и занижение' },
  { name: 'interior', description: 'Интерьер и салон' },
  { name: 'lighting', description: 'Освещение и оптика' },
  { name: 'performance', description: 'Тюнинг двигателя' },
];

const suppliersData = [
  {
    name: 'Автопластик',
    description: 'Российский производитель обвесов и аэродинамических элементов',
    website: 'https://avtoplastik.ru',
    rating: 4.2,
  },
  {
    name: 'БМВ Оригинал',
    description: 'Официальный дилер BMW запчастей',
    website: 'https://bmw.ru',
    rating: 4.8,
  },
  {
    name: 'Спорт Тюнинг',
    description: 'Специализированный магазин спортивных запчастей',
    website: 'https://sport-tuning.ru',
    rating: 4.5,
  },
  {
    name: 'Колеса Центр',
    description: 'Крупнейший поставщик дисков и шин',
    website: 'https://kolesa-center.ru',
    rating: 4.3,
  },
  {
    name: 'Выхлоп Мастер',
    description: 'Производитель выхлопных систем',
    website: 'https://exhaust-master.ru',
    rating: 4.1,
  },
];

const partsData = [
  // Спойлеры
  {
    name: 'Задний спойлер базовый',
    categoryName: 'spoiler',
    description: 'Классический задний спойлер из ABS пластика',
    averagePrice: 85,
    priceRub: 8000,
    installationTime: '1-2 часа',
    installationComplexity: 'medium',
    brand: 'Автопластик',
    partNumber: 'AP-SP-001',
    availability: 'В наличии',
    deliveryDaysMin: 1,
    deliveryDaysMax: 2,
    warrantyMonths: 6,
    qualityRating: 4.0,
    material: 'ABS пластик',
    origin: 'Россия',
  },
  {
    name: 'Спойлер карбоновый спортивный',
    categoryName: 'spoiler',
    description: 'Спортивный спойлер из карбона высокого качества',
    averagePrice: 320,
    priceRub: 30000,
    installationTime: '2-3 часа',
    installationComplexity: 'hard',
    brand: 'Спорт Тюнинг',
    partNumber: 'ST-CARB-SP-002',
    availability: 'Под заказ',
    deliveryDaysMin: 3,
    deliveryDaysMax: 7,
    warrantyMonths: 12,
    qualityRating: 4.7,
    material: 'Карбон',
    origin: 'Германия',
  },
  // Обвесы
  {
    name: 'Обвес M-Sport полный',
    categoryName: 'body_kit',
    description: 'Полный обвес M-Sport для BMW 3 Series',
    averagePrice: 1200,
    priceRub: 114000,
    installationTime: '6-8 часов',
    installationComplexity: 'professional',
    brand: 'БМВ Оригинал',
    partNumber: 'BMW-MS-F30-KIT',
    availability: 'Под заказ',
    deliveryDaysMin: 5,
    deliveryDaysMax: 10,
    warrantyMonths: 24,
    qualityRating: 4.9,
    material: 'Полиуретан',
    origin: 'Германия',
  },
  {
    name: 'Передний сплиттер карбон',
    categoryName: 'body_kit',
    description: 'Карбоновый передний сплиттер для спортивного вида',
    averagePrice: 280,
    priceRub: 26500,
    installationTime: '1-2 часа',
    installationComplexity: 'easy',
    brand: 'Спорт Тюнинг',
    partNumber: 'ST-FRONT-SPLIT-001',
    availability: 'В наличии',
    deliveryDaysMin: 1,
    deliveryDaysMax: 3,
    warrantyMonths: 12,
    qualityRating: 4.5,
    material: 'Карбон',
    origin: 'Китай',
  },
  // Диски
  {
    name: 'Диски BBS 19" черные',
    categoryName: 'wheels',
    description: 'Спортивные диски BBS 19 дюймов в черном цвете',
    averagePrice: 1000,
    priceRub: 95000,
    installationTime: '1-2 часа',
    installationComplexity: 'easy',
    brand: 'BBS',
    partNumber: 'BBS-CH-R-19-BLACK',
    availability: 'В наличии',
    deliveryDaysMin: 2,
    deliveryDaysMax: 4,
    warrantyMonths: 24,
    qualityRating: 4.8,
    material: 'Алюминий',
    origin: 'Германия',
  },
  {
    name: 'Диски OZ Racing 18"',
    categoryName: 'wheels',
    description: 'Легкосплавные диски OZ Racing 18 дюймов',
    averagePrice: 750,
    priceRub: 71000,
    installationTime: '1-2 часа',
    installationComplexity: 'easy',
    brand: 'OZ Racing',
    partNumber: 'OZ-ULT-18-SIL',
    availability: 'В наличии',
    deliveryDaysMin: 1,
    deliveryDaysMax: 3,
    warrantyMonths: 24,
    qualityRating: 4.6,
    material: 'Алюминий',
    origin: 'Италия',
  },
  // Выхлопные системы
  {
    name: 'Выхлопная система спортивная',
    categoryName: 'exhaust',
    description: 'Спортивная выхлопная система с улучшенным звуком',
    averagePrice: 650,
    priceRub: 61750,
    installationTime: '3-4 часа',
    installationComplexity: 'medium',
    brand: 'Выхлоп Мастер',
    partNumber: 'EM-SPORT-EXH-001',
    availability: 'В наличии',
    deliveryDaysMin: 2,
    deliveryDaysMax: 5,
    warrantyMonths: 12,
    qualityRating: 4.3,
    material: 'Нержавеющая сталь',
    origin: 'Россия',
  },
  // Подвеска
  {
    name: 'Пружины занижения H&R',
    categoryName: 'suspension',
    description: 'Пружины занижения H&R для улучшения управляемости',
    averagePrice: 420,
    priceRub: 39900,
    installationTime: '4-6 часов',
    installationComplexity: 'hard',
    brand: 'H&R',
    partNumber: 'HR-SPRING-LOW-001',
    availability: 'Под заказ',
    deliveryDaysMin: 7,
    deliveryDaysMax: 14,
    warrantyMonths: 24,
    qualityRating: 4.7,
    material: 'Сталь',
    origin: 'Германия',
  },
];

async function seedPartsDatabase() {
  try {
    logger.info('🌱 Начинаем заполнение базы данных запчастей...');

    // Создаем категории
    logger.info('📂 Создаем категории...');
    for (const category of categoriesData) {
      await prisma.modificationCategory.upsert({
        where: { name: category.name },
        update: {},
        create: category,
      });
    }

    // Создаем поставщиков
    logger.info('🏪 Создаем поставщиков...');
    for (const supplier of suppliersData) {
      await prisma.supplier.upsert({
        where: { name: supplier.name },
        update: {},
        create: supplier,
      });
    }

    // Создаем запчасти
    logger.info('🔧 Создаем запчасти...');
    for (const part of partsData) {
      const category = await prisma.modificationCategory.findUnique({
        where: { name: part.categoryName },
      });

      if (!category) {
        logger.error(`Категория ${part.categoryName} не найдена`);
        continue;
      }

      const { categoryName, ...partData } = part;
      
      // Проверяем, существует ли уже такая запчасть
      const existingPart = await prisma.modificationPart.findFirst({
        where: {
          OR: [
            { partNumber: part.partNumber },
            { name: part.name, brand: part.brand }
          ]
        }
      });

      if (!existingPart) {
        await prisma.modificationPart.create({
          data: {
            ...partData,
            categoryId: category.id,
            partNumber: part.partNumber || `${part.name}-${Date.now()}`,
          },
        });
      }
    }

    logger.info('✅ База данных запчастей успешно заполнена!');
    
    // Выводим статистику
    const categoriesCount = await prisma.modificationCategory.count();
    const suppliersCount = await prisma.supplier.count();
    const partsCount = await prisma.modificationPart.count();
    
    logger.info(`📊 Статистика:`);
    logger.info(`   Категорий: ${categoriesCount}`);
    logger.info(`   Поставщиков: ${suppliersCount}`);
    logger.info(`   Запчастей: ${partsCount}`);

  } catch (error) {
    logger.error('❌ Ошибка при заполнении базы данных:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем скрипт если он вызван напрямую
if (require.main === module) {
  seedPartsDatabase()
    .then(() => {
      logger.info('🎉 Скрипт заполнения завершен успешно!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Скрипт завершился с ошибкой:', error);
      process.exit(1);
    });
}

export { seedPartsDatabase };
