import { z } from 'zod';
import { ModificationCategory, InstallationComplexity } from '@/types';
export declare const CarInfoSchema: z.ZodObject<{
    id: z.ZodString;
    make: z.ZodString;
    model: z.ZodString;
    year: z.ZodNumber;
    generation: z.ZodOptional<z.ZodString>;
    bodyType: z.ZodString;
    confidence: z.ZodNumber;
    detectedFeatures: z.Zod<PERSON>y<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    model: string;
    confidence: number;
    id: string;
    make: string;
    year: number;
    bodyType: string;
    detectedFeatures: string[];
    generation?: string | undefined;
}, {
    model: string;
    confidence: number;
    id: string;
    make: string;
    year: number;
    bodyType: string;
    detectedFeatures: string[];
    generation?: string | undefined;
}>;
export declare const ModificationRequestSchema: z.ZodObject<{
    carInfo: z.ZodObject<{
        id: z.ZodString;
        make: z.ZodString;
        model: z.Zod<PERSON>tring;
        year: z.<PERSON>od<PERSON>umber;
        generation: z.ZodOptional<z.ZodString>;
        bodyType: z.ZodString;
        confidence: z.ZodNumber;
        detectedFeatures: z.ZodArray<z.ZodString, "many">;
    }, "strip", z.ZodTypeAny, {
        model: string;
        confidence: number;
        id: string;
        make: string;
        year: number;
        bodyType: string;
        detectedFeatures: string[];
        generation?: string | undefined;
    }, {
        model: string;
        confidence: number;
        id: string;
        make: string;
        year: number;
        bodyType: string;
        detectedFeatures: string[];
        generation?: string | undefined;
    }>;
    requestedModifications: z.ZodArray<z.ZodString, "many">;
    userPreferences: z.ZodOptional<z.ZodObject<{
        budget: z.ZodOptional<z.ZodNumber>;
        style: z.ZodOptional<z.ZodEnum<["sport", "luxury", "aggressive", "elegant"]>>;
        priority: z.ZodOptional<z.ZodEnum<["visual", "performance", "balanced"]>>;
    }, "strip", z.ZodTypeAny, {
        budget?: number | undefined;
        style?: "sport" | "luxury" | "aggressive" | "elegant" | undefined;
        priority?: "performance" | "visual" | "balanced" | undefined;
    }, {
        budget?: number | undefined;
        style?: "sport" | "luxury" | "aggressive" | "elegant" | undefined;
        priority?: "performance" | "visual" | "balanced" | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    carInfo: {
        model: string;
        confidence: number;
        id: string;
        make: string;
        year: number;
        bodyType: string;
        detectedFeatures: string[];
        generation?: string | undefined;
    };
    requestedModifications: string[];
    userPreferences?: {
        budget?: number | undefined;
        style?: "sport" | "luxury" | "aggressive" | "elegant" | undefined;
        priority?: "performance" | "visual" | "balanced" | undefined;
    } | undefined;
}, {
    carInfo: {
        model: string;
        confidence: number;
        id: string;
        make: string;
        year: number;
        bodyType: string;
        detectedFeatures: string[];
        generation?: string | undefined;
    };
    requestedModifications: string[];
    userPreferences?: {
        budget?: number | undefined;
        style?: "sport" | "luxury" | "aggressive" | "elegant" | undefined;
        priority?: "performance" | "visual" | "balanced" | undefined;
    } | undefined;
}>;
export declare const UserInputSchema: z.ZodObject<{
    text: z.ZodString;
    userId: z.ZodNumber;
    chatId: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    userId: number;
    chatId: number;
    text: string;
}, {
    userId: number;
    chatId: number;
    text: string;
}>;
export declare const FileValidationSchema: z.ZodObject<{
    fileId: z.ZodString;
    fileSize: z.ZodNumber;
    mimeType: z.ZodString;
}, "strip", z.ZodTypeAny, {
    fileId: string;
    fileSize: number;
    mimeType: string;
}, {
    fileId: string;
    fileSize: number;
    mimeType: string;
}>;
export declare const PricingSchema: z.ZodObject<{
    specificPart: z.ZodString;
    brand: z.ZodString;
    partNumber: z.ZodOptional<z.ZodString>;
    price: z.ZodNumber;
    averagePrice: z.ZodNumber;
    installationTime: z.ZodString;
    description: z.ZodString;
}, "strip", z.ZodTypeAny, {
    specificPart: string;
    brand: string;
    price: number;
    averagePrice: number;
    installationTime: string;
    description: string;
    partNumber?: string | undefined;
}, {
    specificPart: string;
    brand: string;
    price: number;
    averagePrice: number;
    installationTime: string;
    description: string;
    partNumber?: string | undefined;
}>;
export declare const ModificationCategorySchema: z.ZodNativeEnum<typeof ModificationCategory>;
export declare const InstallationComplexitySchema: z.ZodNativeEnum<typeof InstallationComplexity>;
export declare class ValidationUtils {
    static validateCarInfo(data: unknown): {
        model: string;
        confidence: number;
        id: string;
        make: string;
        year: number;
        bodyType: string;
        detectedFeatures: string[];
        generation?: string | undefined;
    };
    static validateModificationRequest(data: unknown): {
        carInfo: {
            model: string;
            confidence: number;
            id: string;
            make: string;
            year: number;
            bodyType: string;
            detectedFeatures: string[];
            generation?: string | undefined;
        };
        requestedModifications: string[];
        userPreferences?: {
            budget?: number | undefined;
            style?: "sport" | "luxury" | "aggressive" | "elegant" | undefined;
            priority?: "performance" | "visual" | "balanced" | undefined;
        } | undefined;
    };
    static validateUserInput(data: unknown): {
        userId: number;
        chatId: number;
        text: string;
    };
    static validateFile(data: unknown): {
        fileId: string;
        fileSize: number;
        mimeType: string;
    };
    static validatePricing(data: unknown): {
        specificPart: string;
        brand: string;
        price: number;
        averagePrice: number;
        installationTime: string;
        description: string;
        partNumber?: string | undefined;
    };
    static sanitizeText(text: string): string;
    static isValidImageUrl(url: string): boolean;
    static validateModificationText(text: string): boolean;
    static extractModificationKeywords(text: string): string[];
    static validatePriceRange(min: number, max: number): boolean;
    static validateCarYear(year: number): boolean;
    static containsInappropriateContent(text: string): boolean;
    static validateSessionData(data: any): boolean;
    static validateRateLimit(requestCount: number, timeWindow: number, maxRequests: number): boolean;
}
//# sourceMappingURL=validation.d.ts.map