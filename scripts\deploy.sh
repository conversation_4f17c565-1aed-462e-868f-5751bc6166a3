#!/bin/bash

# Car Modification Generator Deployment Script

set -e

echo "🚀 Deploying Car Modification Generator..."

# Configuration
IMAGE_NAME="car-modification-generator"
CONTAINER_NAME="car-mod-app"
BACKUP_DIR="./backups"

# Parse command line arguments
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

echo "📋 Deployment Configuration:"
echo "   Environment: $ENVIRONMENT"
echo "   Version: $VERSION"
echo "   Image: $IMAGE_NAME:$VERSION"

# Create backup directory
mkdir -p $BACKUP_DIR

# Function to backup database
backup_database() {
    echo "💾 Creating database backup..."
    BACKUP_FILE="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if docker ps | grep -q postgres; then
        docker exec car-mod-postgres pg_dump -U postgres car_modifications > $BACKUP_FILE
        echo "✅ Database backup created: $BACKUP_FILE"
    else
        echo "⚠️  PostgreSQL container not running, skipping backup"
    fi
}

# Function to build Docker image
build_image() {
    echo "🔨 Building Docker image..."
    docker build -t $IMAGE_NAME:$VERSION .
    docker tag $IMAGE_NAME:$VERSION $IMAGE_NAME:latest
    echo "✅ Docker image built successfully"
}

# Function to run tests
run_tests() {
    echo "🧪 Running tests..."
    npm test
    echo "✅ All tests passed"
}

# Function to deploy application
deploy_app() {
    echo "🚀 Deploying application..."
    
    # Stop existing container if running
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "🛑 Stopping existing container..."
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
    fi
    
    # Start new container
    echo "▶️  Starting new container..."
    docker-compose up -d
    
    # Wait for application to be ready
    echo "⏳ Waiting for application to be ready..."
    sleep 30
    
    # Health check
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        echo "✅ Application is healthy"
    else
        echo "❌ Application health check failed"
        exit 1
    fi
}

# Function to rollback deployment
rollback() {
    echo "🔄 Rolling back deployment..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t $BACKUP_DIR/db_backup_*.sql | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        echo "📥 Restoring database from: $LATEST_BACKUP"
        docker exec -i car-mod-postgres psql -U postgres car_modifications < $LATEST_BACKUP
    fi
    
    # Restart with previous version
    docker-compose down
    docker-compose up -d
    
    echo "✅ Rollback completed"
}

# Function to cleanup old images
cleanup() {
    echo "🧹 Cleaning up old Docker images..."
    docker image prune -f
    
    # Keep only last 5 backups
    ls -t $BACKUP_DIR/db_backup_*.sql | tail -n +6 | xargs -r rm
    
    echo "✅ Cleanup completed"
}

# Main deployment flow
case "$ENVIRONMENT" in
    "production")
        echo "🏭 Production deployment"
        backup_database
        run_tests
        build_image
        deploy_app
        cleanup
        ;;
    "staging")
        echo "🧪 Staging deployment"
        run_tests
        build_image
        deploy_app
        ;;
    "rollback")
        echo "🔄 Rolling back deployment"
        rollback
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo "Usage: $0 [production|staging|rollback] [version]"
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 Application Status:"
echo "   URL: http://localhost:3000"
echo "   Health: http://localhost:3000/health"
echo "   Logs: docker logs $CONTAINER_NAME"
echo ""
echo "🔧 Management Commands:"
echo "   View logs: docker logs -f $CONTAINER_NAME"
echo "   Stop app: docker-compose down"
echo "   Restart: docker-compose restart"
echo "   Rollback: $0 rollback"
