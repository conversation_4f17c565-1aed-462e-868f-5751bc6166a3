# 🚀 Полная инструкция по запуску PimpMyRideAI

Эта инструкция поможет вам запустить Telegram бота для генерации модификаций автомобилей с нуля до полностью рабочего состояния.

## 📋 Предварительные требования

### Системные требования
- **Windows 10/11** или **macOS** или **Linux**
- **Node.js 18+** 
- **PostgreSQL 12+**
- **Git**
- **Минимум 4GB RAM**

## 🔧 Шаг 1: Настройка окружения

### 1.1 Установка Node.js

**Windows:**
1. Скачайте Node.js с https://nodejs.org/
2. Выберите LTS версию (18.x или новее)
3. Запустите установщик и следуйте инструкциям
4. Проверьте установку:
```bash
node --version  # должно показать v18.x.x или выше
npm --version   # должно показать 9.x.x или выше
```

**macOS:**
```bash
# Через Homebrew (рекомендуется)
brew install node@18

# Или скачайте с официального сайта
```

**Linux (Ubuntu/Debian):**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 1.2 Установка PostgreSQL

**Windows:**
1. Скачайте PostgreSQL с https://www.postgresql.org/download/windows/
2. Запустите установщик
3. Запомните пароль для пользователя `postgres`
4. Убедитесь, что сервис запущен

**macOS:**
```bash
# Через Homebrew
brew install postgresql@14
brew services start postgresql@14
```

**Linux:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 1.3 Клонирование проекта

```bash
# Клонируйте репозиторий
git clone https://github.com/DoubleBe/PimpMyRideAI.git
cd PimpMyRideAI

# Установите зависимости
npm install
```

## 🔑 Шаг 2: Получение API ключей

### 2.1 Telegram Bot Token

1. **Откройте Telegram** и найдите @BotFather
2. **Отправьте команду** `/newbot`
3. **Введите имя бота** (например: "PimpMyRideAI Bot")
4. **Введите username** (например: "pimpmyride_ai_bot")
5. **Скопируйте токен** (формат: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 2.2 ProxyAPI.ru (для генерации изображений)

1. **Зарегистрируйтесь** на https://proxyapi.ru/
2. **Пополните баланс** (минимум $5 для тестирования)
3. **Получите API ключ** в личном кабинете
4. **Убедитесь**, что доступна модель `gpt-image-1`

### 2.3 OpenRouter.ai (для анализа изображений)

1. **Зарегистрируйтесь** на https://openrouter.ai/
2. **Пополните баланс** (минимум $5)
3. **Создайте API ключ** в настройках
4. **Проверьте доступ** к моделям `openai/gpt-4o` и `openai/gpt-4o-mini`

### 2.4 ABCP.ru (опционально, для реальных цен)

1. **Зарегистрируйтесь** на https://www.abcp.ru/
2. **Подайте заявку** на API доступ через поддержку
3. **Дождитесь одобрения** (может занять 1-3 дня)
4. **Получите API ключ**

## 🗄️ Шаг 3: Настройка базы данных

### 3.1 Создание базы данных

```bash
# Подключитесь к PostgreSQL
psql -U postgres

# Создайте базу данных
CREATE DATABASE car_modifications;

# Создайте пользователя (опционально)
CREATE USER pimpmyride WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE car_modifications TO pimpmyride;

# Выйдите из psql
\q
```

### 3.2 Настройка Prisma

```bash
# Сгенерируйте Prisma клиент
npm run db:generate

# Примените схему к базе данных
npm run db:push
```

## ⚙️ Шаг 4: Конфигурация

### 4.1 Создание .env файла

```bash
# Скопируйте пример конфигурации
cp .env.example .env
```

### 4.2 Заполнение .env файла

Откройте `.env` файл и заполните следующие обязательные поля:

```env
# ОБЯЗАТЕЛЬНЫЕ НАСТРОЙКИ
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
PROXYAPI_KEY=your_proxyapi_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/car_modifications

# ОПЦИОНАЛЬНЫЕ НАСТРОЙКИ
ABCP_API_KEY=your_abcp_key_here
EXIST_API_KEY=your_exist_key_here

# НАСТРОЙКИ СЕРВЕРА
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# НАСТРОЙКИ ФАЙЛОВ
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
```

### 4.3 Создание необходимых папок

```bash
# Создайте папки для логов и загрузок
mkdir -p logs uploads
```

## 🧪 Шаг 5: Тестирование

### 5.1 Проверка конфигурации

```bash
# Проверьте синтаксис TypeScript
npm run build

# Запустите тесты (если есть)
npm test
```

### 5.2 Тестирование API

Создайте файл `test-setup.js`:

```javascript
const axios = require('axios');

async function testAPIs() {
  console.log('🧪 Тестирование API...');
  
  // Тест ProxyAPI
  try {
    const response = await axios.get('https://api.proxyapi.ru/openai/v1/models', {
      headers: { 'Authorization': `Bearer ${process.env.PROXYAPI_KEY}` }
    });
    console.log('✅ ProxyAPI работает');
  } catch (error) {
    console.log('❌ ProxyAPI недоступен:', error.message);
  }
  
  // Тест OpenRouter
  try {
    const response = await axios.get('https://openrouter.ai/api/v1/models', {
      headers: { 'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}` }
    });
    console.log('✅ OpenRouter работает');
  } catch (error) {
    console.log('❌ OpenRouter недоступен:', error.message);
  }
}

testAPIs();
```

Запустите тест:
```bash
node test-setup.js
```

## 🚀 Шаг 6: Запуск бота

### 6.1 Первый запуск

```bash
# Запуск в режиме разработки
npm run dev
```

Вы должны увидеть:
```
[INFO] Starting Car Modification Generator Bot...
[INFO] Environment: development
[INFO] 🚗 Car Modification Generator Bot started successfully!
[INFO] Bot is ready to receive messages...
```

### 6.2 Тестирование в Telegram

1. **Найдите своего бота** в Telegram по username
2. **Отправьте** `/start`
3. **Загрузите фото автомобиля** (желательно вид сбоку)
4. **Дождитесь распознавания** автомобиля
5. **Опишите модификации** (например: "Добавь спортивный обвес и черные диски")
6. **Получите результат** с изображением и ценами

## 🔧 Шаг 7: Продакшн настройки

### 7.1 Сборка для продакшна

```bash
# Соберите проект
npm run build

# Запустите в продакшн режиме
NODE_ENV=production npm start
```

### 7.2 Настройка автозапуска (Linux)

Создайте systemd сервис `/etc/systemd/system/pimpmyride.service`:

```ini
[Unit]
Description=PimpMyRideAI Bot
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/PimpMyRideAI
ExecStart=/usr/bin/node dist/index.js
Restart=always
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

Активируйте сервис:
```bash
sudo systemctl enable pimpmyride
sudo systemctl start pimpmyride
```

## 🐛 Устранение проблем

### Частые ошибки

**1. "Cannot connect to database"**
- Проверьте, что PostgreSQL запущен
- Убедитесь в правильности DATABASE_URL
- Проверьте права доступа к базе

**2. "Telegram API error"**
- Проверьте правильность TELEGRAM_BOT_TOKEN
- Убедитесь, что бот не заблокирован

**3. "ProxyAPI/OpenRouter error"**
- Проверьте баланс на счету
- Убедитесь в правильности API ключей
- Проверьте доступность моделей

**4. "Module not found"**
- Выполните `npm install` заново
- Проверьте версию Node.js

### Логи и отладка

```bash
# Просмотр логов в реальном времени
tail -f logs/app.log

# Запуск с подробными логами
LOG_LEVEL=debug npm run dev
```

## 📊 Мониторинг

### Проверка состояния

```bash
# Проверка процесса
ps aux | grep node

# Проверка портов
netstat -tlnp | grep 3000

# Проверка логов
tail -n 50 logs/app.log
```

## 🎉 Готово!

Ваш PimpMyRideAI бот готов к работе! Теперь пользователи могут:

1. ✅ Загружать фото автомобилей
2. ✅ Получать AI-распознавание марки и модели
3. ✅ Запрашивать модификации на естественном языке
4. ✅ Получать реалистичные визуализации
5. ✅ Видеть актуальные цены на запчасти
6. ✅ Получать инструкции по установке

**Следующие шаги:**
- Настройте мониторинг и алерты
- Добавьте больше API поставщиков запчастей
- Расширьте базу совместимых автомобилей
- Добавьте веб-интерфейс для администрирования
