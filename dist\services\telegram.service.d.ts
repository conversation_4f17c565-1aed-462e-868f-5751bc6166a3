export declare class TelegramService {
    private bot;
    private sessionManager;
    private modificationService;
    private costFormatter;
    constructor();
    private setupHandlers;
    private handleStart;
    private handleHelp;
    private handlePhoto;
    private handleTextMessage;
    private handleModificationRequest;
    private sendModificationResults;
    private downloadImageAsBuffer;
    private sendMessage;
    start(): void;
    stop(): void;
}
//# sourceMappingURL=telegram.service.d.ts.map