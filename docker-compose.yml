version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: car-mod-postgres
    environment:
      POSTGRES_DB: car_modifications
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (optional)
  redis:
    image: redis:7-alpine
    container_name: car-mod-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: car-mod-app
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/car_modifications?schema=public
      REDIS_URL: redis://redis:6379
      PORT: 3000
    ports:
      - "3000:3000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development Database (for development only)
  postgres-dev:
    image: postgres:15-alpine
    container_name: car-mod-postgres-dev
    environment:
      POSTGRES_DB: car_modifications_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    profiles:
      - dev

volumes:
  postgres_data:
    driver: local
  postgres_dev_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: car-modification-network
