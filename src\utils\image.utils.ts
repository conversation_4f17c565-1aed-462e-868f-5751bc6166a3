import sharp from 'sharp';
import axios from 'axios';
import { logger } from './logger';
import { serverConfig } from '@/config';
import fs from 'fs/promises';
import path from 'path';

export class ImageUtils {
  /**
   * Download image from URL and save locally
   */
  static async downloadImage(imageUrl: string, filename: string): Promise<string> {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000,
      });

      const buffer = Buffer.from(response.data);
      const uploadDir = serverConfig.uploadDir;
      
      // Ensure upload directory exists
      await fs.mkdir(uploadDir, { recursive: true });
      
      const filePath = path.join(uploadDir, filename);
      await fs.writeFile(filePath, buffer);
      
      logger.info(`Image downloaded and saved: ${filePath}`);
      return filePath;
    } catch (error) {
      logger.error('Error downloading image:', error);
      throw new Error('Failed to download image');
    }
  }

  /**
   * Resize image to specified dimensions
   */
  static async resizeImage(
    inputPath: string,
    outputPath: string,
    width: number,
    height: number
  ): Promise<void> {
    try {
      await sharp(inputPath)
        .resize(width, height, {
          fit: 'cover',
          position: 'center',
        })
        .jpeg({ quality: 90 })
        .toFile(outputPath);
      
      logger.info(`Image resized: ${outputPath}`);
    } catch (error) {
      logger.error('Error resizing image:', error);
      throw new Error('Failed to resize image');
    }
  }

  /**
   * Convert image to specific format
   */
  static async convertImage(
    inputPath: string,
    outputPath: string,
    format: 'jpeg' | 'png' | 'webp'
  ): Promise<void> {
    try {
      const sharpInstance = sharp(inputPath);
      
      switch (format) {
        case 'jpeg':
          await sharpInstance.jpeg({ quality: 90 }).toFile(outputPath);
          break;
        case 'png':
          await sharpInstance.png({ quality: 90 }).toFile(outputPath);
          break;
        case 'webp':
          await sharpInstance.webp({ quality: 90 }).toFile(outputPath);
          break;
      }
      
      logger.info(`Image converted to ${format}: ${outputPath}`);
    } catch (error) {
      logger.error('Error converting image:', error);
      throw new Error('Failed to convert image');
    }
  }

  /**
   * Get image metadata
   */
  static async getImageMetadata(imagePath: string): Promise<sharp.Metadata> {
    try {
      const metadata = await sharp(imagePath).metadata();
      return metadata;
    } catch (error) {
      logger.error('Error getting image metadata:', error);
      throw new Error('Failed to get image metadata');
    }
  }

  /**
   * Optimize image for web
   */
  static async optimizeForWeb(
    inputPath: string,
    outputPath: string,
    maxWidth: number = 1024
  ): Promise<void> {
    try {
      const metadata = await sharp(inputPath).metadata();
      const width = metadata.width || maxWidth;
      const height = metadata.height || Math.round((maxWidth * (metadata.height || 768)) / width);
      
      await sharp(inputPath)
        .resize(Math.min(width, maxWidth), Math.min(height, Math.round((maxWidth * height) / width)), {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ 
          quality: 85,
          progressive: true,
        })
        .toFile(outputPath);
      
      logger.info(`Image optimized for web: ${outputPath}`);
    } catch (error) {
      logger.error('Error optimizing image:', error);
      throw new Error('Failed to optimize image');
    }
  }

  /**
   * Create thumbnail
   */
  static async createThumbnail(
    inputPath: string,
    outputPath: string,
    size: number = 200
  ): Promise<void> {
    try {
      await sharp(inputPath)
        .resize(size, size, {
          fit: 'cover',
          position: 'center',
        })
        .jpeg({ quality: 80 })
        .toFile(outputPath);
      
      logger.info(`Thumbnail created: ${outputPath}`);
    } catch (error) {
      logger.error('Error creating thumbnail:', error);
      throw new Error('Failed to create thumbnail');
    }
  }

  /**
   * Validate image file
   */
  static async validateImage(imagePath: string): Promise<boolean> {
    try {
      const metadata = await sharp(imagePath).metadata();

      // Check if it's a valid image
      if (!metadata.width || !metadata.height) {
        return false;
      }

      // Check file size (max 10MB)
      const stats = await fs.stat(imagePath);
      if (stats.size > serverConfig.maxFileSize) {
        return false;
      }

      // Check dimensions (reasonable limits)
      if (metadata.width > 4096 || metadata.height > 4096) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error validating image:', error);
      return false;
    }
  }

  /**
   * Validate image for editing API requirements
   */
  static async validateImageForEditing(imagePath: string): Promise<{
    isValid: boolean;
    errors: string[];
    metadata?: any;
  }> {
    const errors: string[] = [];

    try {
      const metadata = await sharp(imagePath).metadata();

      // Check if it's a valid image
      if (!metadata.width || !metadata.height) {
        errors.push('Invalid image dimensions');
      }

      // Check file size (max 4MB for editing APIs)
      const stats = await fs.stat(imagePath);
      if (stats.size > 4 * 1024 * 1024) {
        errors.push('Image too large for editing API (max 4MB)');
      }

      // Check minimum dimensions
      if (metadata.width && metadata.width < 256) {
        errors.push('Image width too small (minimum 256px)');
      }
      if (metadata.height && metadata.height < 256) {
        errors.push('Image height too small (minimum 256px)');
      }

      // Check maximum dimensions for editing (updated for Full HD support)
      if (metadata.width && metadata.width > 1920) {
        errors.push('Image width too large for editing (maximum 1920px for Full HD support)');
      }
      if (metadata.height && metadata.height > 1080) {
        errors.push('Image height too large for editing (maximum 1080px for Full HD support)');
      }

      // Check format compatibility
      if (metadata.format && !['jpeg', 'jpg', 'png', 'webp'].includes(metadata.format)) {
        errors.push(`Unsupported format: ${metadata.format}. Use JPEG, PNG, or WebP`);
      }

      return {
        isValid: errors.length === 0,
        errors,
        metadata
      };
    } catch (error) {
      logger.error('Error validating image for editing:', error);
      return {
        isValid: false,
        errors: ['Failed to read image file']
      };
    }
  }

  /**
   * Prepare image for editing API with Full HD support (preserve resolution, format, optimize)
   */
  static async prepareForEditing(
    inputPath: string,
    outputPath: string,
    maxSize: number = 1920, // Updated to Full HD width
    maxFileSize: number = 4 * 1024 * 1024 // 4MB
  ): Promise<void> {
    try {
      const metadata = await sharp(inputPath).metadata();
      const originalWidth = metadata.width || 1024;
      const originalHeight = metadata.height || 1024;

      // Calculate optimal dimensions for Full HD support
      const { targetWidth, targetHeight, resizeNeeded } = this.calculateOptimalDimensions(
        originalWidth,
        originalHeight,
        maxSize
      );

      let sharpInstance = sharp(inputPath);

      if (resizeNeeded) {
        sharpInstance = sharpInstance.resize(targetWidth, targetHeight, {
          fit: 'inside',
          withoutEnlargement: true,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        });
        logger.info(`Image resized from ${originalWidth}x${originalHeight} to ${targetWidth}x${targetHeight} (Full HD optimized)`);
      } else {
        logger.info(`Preserving original resolution: ${originalWidth}x${originalHeight} (within Full HD bounds)`);
      }

      // Convert to PNG format for editing APIs with high quality
      await sharpInstance
        .png({ quality: 95, compressionLevel: 6, progressive: true }) // Higher quality for better editing results
        .toFile(outputPath);

      // Check file size and compress if needed
      const stats = await fs.stat(outputPath);
      if (stats.size > maxFileSize) {
        logger.info(`File size ${stats.size} exceeds limit, compressing...`);
        await sharp(outputPath)
          .png({ quality: 85, compressionLevel: 9, progressive: true })
          .toFile(outputPath + '.tmp');

        await fs.rename(outputPath + '.tmp', outputPath);
        logger.info('Image compressed to meet file size requirements');
      }

      logger.info(`Image prepared for editing: ${outputPath} (${targetWidth}x${targetHeight})`);
    } catch (error) {
      logger.error('Error preparing image for editing:', error);
      throw new Error('Failed to prepare image for editing');
    }
  }

  /**
   * Calculate optimal dimensions for Full HD support while preserving aspect ratio
   */
  static calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxSize: number = 1920
  ): { targetWidth: number; targetHeight: number; resizeNeeded: boolean } {
    const aspectRatio = originalWidth / originalHeight;
    const fullHDWidth = Math.min(maxSize, 1920); // Use maxSize but cap at Full HD
    const fullHDHeight = 1080;
    const fullHDAspectRatio = fullHDWidth / fullHDHeight;

    let targetWidth = originalWidth;
    let targetHeight = originalHeight;
    let resizeNeeded = false;

    // Check if image exceeds Full HD bounds
    if (originalWidth > fullHDWidth || originalHeight > fullHDHeight) {
      resizeNeeded = true;

      // Maintain aspect ratio within Full HD bounds
      if (aspectRatio > fullHDAspectRatio) {
        // Image is wider than 16:9, fit to width
        targetWidth = fullHDWidth;
        targetHeight = Math.round(fullHDWidth / aspectRatio);
      } else {
        // Image is taller than 16:9, fit to height
        targetHeight = fullHDHeight;
        targetWidth = Math.round(fullHDHeight * aspectRatio);
      }
    } else if (originalWidth < 512 && originalHeight < 512) {
      // Only upscale if BOTH dimensions are below 512px
      resizeNeeded = true;
      const minSize = 512;

      if (originalWidth < originalHeight) {
        // Portrait: scale based on width
        targetWidth = minSize;
        targetHeight = Math.round(minSize / aspectRatio);
      } else {
        // Landscape or square: scale based on height
        targetHeight = minSize;
        targetWidth = Math.round(minSize * aspectRatio);
      }
    }

    // Ensure dimensions are even numbers (some APIs prefer this)
    targetWidth = Math.round(targetWidth / 2) * 2;
    targetHeight = Math.round(targetHeight / 2) * 2;

    return { targetWidth, targetHeight, resizeNeeded };
  }

  /**
   * Download image from URL and prepare for editing with Full HD support
   */
  static async downloadAndPrepareForEditing(
    imageUrl: string,
    outputPath: string,
    maxSize: number = 1920, // Updated to Full HD width
    maxFileSize: number = 4 * 1024 * 1024 // 4MB
  ): Promise<Buffer> {
    try {
      // Download image
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 15000, // Increased timeout for larger images
      });

      const originalBuffer = Buffer.from(response.data);
      const metadata = await sharp(originalBuffer).metadata();
      const originalWidth = metadata.width || 1024;
      const originalHeight = metadata.height || 1024;

      // Calculate optimal dimensions for Full HD support
      const { targetWidth, targetHeight, resizeNeeded } = this.calculateOptimalDimensions(
        originalWidth,
        originalHeight,
        maxSize
      );

      let sharpInstance = sharp(originalBuffer);

      if (resizeNeeded) {
        sharpInstance = sharpInstance.resize(targetWidth, targetHeight, {
          fit: 'inside',
          withoutEnlargement: true,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        });
        logger.info(`Downloaded image resized from ${originalWidth}x${originalHeight} to ${targetWidth}x${targetHeight} (Full HD optimized)`);
      } else {
        logger.info(`Downloaded image preserving original resolution: ${originalWidth}x${originalHeight} (within Full HD bounds)`);
      }

      // Process and prepare the image with high quality
      let processedBuffer = await sharpInstance
        .png({ quality: 95, compressionLevel: 6, progressive: true })
        .toBuffer();

      // Check file size and compress if needed
      if (processedBuffer.length > maxFileSize) {
        logger.info(`Downloaded image size ${processedBuffer.length} exceeds limit, compressing...`);
        processedBuffer = await sharp(processedBuffer)
          .png({ quality: 85, compressionLevel: 9, progressive: true })
          .toBuffer();
        logger.info('Downloaded image compressed to meet file size requirements');
      }

      // Save to file if path provided
      if (outputPath) {
        await fs.writeFile(outputPath, processedBuffer);
        logger.info(`Image downloaded and prepared: ${outputPath} (${targetWidth}x${targetHeight})`);
      }

      return processedBuffer;
    } catch (error) {
      logger.error('Error downloading and preparing image:', error);
      throw new Error('Failed to download and prepare image');
    }
  }

  /**
   * Clean up old files
   */
  static async cleanupOldFiles(directory: string, maxAgeHours: number = 24): Promise<void> {
    try {
      const files = await fs.readdir(directory);
      const now = Date.now();
      const maxAge = maxAgeHours * 60 * 60 * 1000;
      
      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          logger.info(`Cleaned up old file: ${filePath}`);
        }
      }
    } catch (error) {
      logger.error('Error cleaning up old files:', error);
    }
  }

  /**
   * Generate unique filename
   */
  static generateFilename(prefix: string = 'img', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}.${extension}`;
  }

  /**
   * Generate a simple mask for car parts modification
   * This creates a basic mask that can be used with image editing APIs
   */
  static async generateCarPartMask(
    imagePath: string,
    outputPath: string,
    partType: 'wheels' | 'body' | 'windows' | 'full'
  ): Promise<void> {
    try {
      const metadata = await sharp(imagePath).metadata();
      const width = metadata.width || 1024;
      const height = metadata.height || 1024;

      let maskBuffer: Buffer;

      switch (partType) {
        case 'wheels':
          // Create mask for lower portion of the car (where wheels typically are)
          maskBuffer = await sharp({
            create: {
              width,
              height,
              channels: 3,
              background: { r: 0, g: 0, b: 0 }
            }
          })
          .composite([
            {
              input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="0" y="${Math.floor(height * 0.7)}" width="${width}" height="${Math.floor(height * 0.3)}" fill="white"/>
              </svg>`),
              top: 0,
              left: 0
            }
          ])
          .png()
          .toBuffer();
          break;

        case 'body':
          // Create mask for car body (middle section)
          maskBuffer = await sharp({
            create: {
              width,
              height,
              channels: 3,
              background: { r: 0, g: 0, b: 0 }
            }
          })
          .composite([
            {
              input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="${Math.floor(width * 0.1)}" y="${Math.floor(height * 0.2)}" width="${Math.floor(width * 0.8)}" height="${Math.floor(height * 0.6)}" fill="white"/>
              </svg>`),
              top: 0,
              left: 0
            }
          ])
          .png()
          .toBuffer();
          break;

        case 'windows':
          // Create mask for upper portion (windows area)
          maskBuffer = await sharp({
            create: {
              width,
              height,
              channels: 3,
              background: { r: 0, g: 0, b: 0 }
            }
          })
          .composite([
            {
              input: Buffer.from(`<svg width="${width}" height="${height}">
                <rect x="${Math.floor(width * 0.2)}" y="0" width="${Math.floor(width * 0.6)}" height="${Math.floor(height * 0.5)}" fill="white"/>
              </svg>`),
              top: 0,
              left: 0
            }
          ])
          .png()
          .toBuffer();
          break;

        case 'full':
        default:
          // Full white mask (edit entire image)
          maskBuffer = await sharp({
            create: {
              width,
              height,
              channels: 3,
              background: { r: 255, g: 255, b: 255 }
            }
          })
          .png()
          .toBuffer();
          break;
      }

      await fs.writeFile(outputPath, maskBuffer);
      logger.info(`Generated ${partType} mask: ${outputPath}`);
    } catch (error) {
      logger.error('Error generating car part mask:', error);
      throw new Error('Failed to generate mask');
    }
  }

  /**
   * Detect modification type from user request and suggest appropriate mask
   */
  static detectModificationType(userRequest: string): 'wheels' | 'body' | 'windows' | 'full' {
    const request = userRequest.toLowerCase();

    if (request.includes('wheel') || request.includes('rim') || request.includes('tire') ||
        request.includes('диск') || request.includes('колес')) {
      return 'wheels';
    }

    if (request.includes('window') || request.includes('tint') || request.includes('glass') ||
        request.includes('окн') || request.includes('стекл') || request.includes('тонир')) {
      return 'windows';
    }

    if (request.includes('body') || request.includes('kit') || request.includes('bumper') ||
        request.includes('spoiler') || request.includes('обвес') || request.includes('бампер') ||
        request.includes('спойлер')) {
      return 'body';
    }

    return 'full'; // Default to full modification
  }

  /**
   * Validate image quality for photorealistic results
   */
  static async validateImageQuality(imagePath: string): Promise<{
    isHighQuality: boolean;
    qualityScore: number;
    recommendations: string[];
  }> {
    try {
      const metadata = await sharp(imagePath).metadata();
      const stats = await sharp(imagePath).stats();

      let qualityScore = 100;
      const recommendations: string[] = [];

      // Check resolution
      const totalPixels = (metadata.width || 0) * (metadata.height || 0);
      if (totalPixels < 500000) { // Less than ~700x700
        qualityScore -= 30;
        recommendations.push('Image resolution is low - consider using a higher resolution photo for better results');
      }

      // Check if image is too dark or too bright
      const brightness = stats.channels?.[0]?.mean || 128;
      if (brightness < 50) {
        qualityScore -= 20;
        recommendations.push('Image appears too dark - modifications may not be clearly visible');
      } else if (brightness > 200) {
        qualityScore -= 15;
        recommendations.push('Image appears overexposed - may affect modification quality');
      }

      // Check contrast (using standard deviation as proxy)
      const stdDev = stats.channels?.[0]?.stdev || 50;
      if (stdDev < 20) {
        qualityScore -= 25;
        recommendations.push('Image has low contrast - modifications may not blend well');
      }

      // Check format
      if (metadata.format === 'jpeg' && metadata.density && metadata.density < 72) {
        qualityScore -= 10;
        recommendations.push('Image has low DPI - may affect detail quality');
      }

      const isHighQuality = qualityScore >= 70;

      return {
        isHighQuality,
        qualityScore,
        recommendations
      };
    } catch (error) {
      logger.error('Error validating image quality:', error);
      return {
        isHighQuality: false,
        qualityScore: 0,
        recommendations: ['Unable to analyze image quality']
      };
    }
  }

  /**
   * Enhance image for better modification results
   */
  static async enhanceImageForModification(
    inputPath: string,
    outputPath: string
  ): Promise<void> {
    try {
      const stats = await sharp(inputPath).stats();

      let sharpInstance = sharp(inputPath);

      // Auto-enhance based on image characteristics
      const brightness = stats.channels?.[0]?.mean || 128;
      const contrast = stats.channels?.[0]?.stdev || 50;

      // Adjust brightness if needed
      if (brightness < 80) {
        sharpInstance = sharpInstance.modulate({ brightness: 1.2 });
        logger.info('Applied brightness enhancement');
      } else if (brightness > 180) {
        sharpInstance = sharpInstance.modulate({ brightness: 0.9 });
        logger.info('Applied brightness reduction');
      }

      // Enhance saturation slightly for better visual appeal
      if (contrast < 30) {
        sharpInstance = sharpInstance.modulate({ saturation: 1.1 });
        logger.info('Applied saturation enhancement for better contrast');
      }

      // Apply subtle sharpening for better detail
      sharpInstance = sharpInstance.sharpen({ sigma: 0.5, m1: 0.5, m2: 2 });

      await sharpInstance
        .png({ quality: 95 })
        .toFile(outputPath);

      logger.info(`Image enhanced for modification: ${outputPath}`);
    } catch (error) {
      logger.error('Error enhancing image:', error);
      throw new Error('Failed to enhance image for modification');
    }
  }
}
